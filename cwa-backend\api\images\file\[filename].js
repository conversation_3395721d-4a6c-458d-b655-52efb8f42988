const { ensureConnection } = require("../../../config/db");
const ImageController = require("../../../controllers/imageController");

module.exports = async (req, res) => {
  try {
    console.log('File/filename API called - URL:', req.url);
    console.log('File/filename API called - Method:', req.method);
    console.log('File/filename API called - Query:', req.query);
    
    // Ensure database connection
    await ensureConnection();
    
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    
    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }
    
    if (req.method !== 'GET') {
      return res.status(405).json({
        status: "error",
        message: "Method not allowed"
      });
    }
    
    // Get filename from query parameter (Vercel automatically parses [filename] from URL)
    const filename = req.query.filename;
    
    if (!filename) {
      return res.status(400).json({
        status: "error",
        message: "Filename is required"
      });
    }
    
    console.log('File/filename API - Looking for filename:', filename);
    
    // Set up params for controller
    req.params = { filename };
    return await ImageController.getImageByFilename(req, res);
    
  } catch (error) {
    console.error("File/filename API error:", error);
    return res.status(500).json({
      status: "error",
      message: "Internal server error",
      error: error.message
    });
  }
};
