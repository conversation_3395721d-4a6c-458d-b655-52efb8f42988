const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");
const path = require("path");
const swaggerUi = require("swagger-ui-express");
const swaggerSpecs = require("../config/swagger");
const { connectDB, ensureConnection } = require("../config/db");
const { connectRedis } = require("../config/redis");
const { connectGridFS } = require("../config/gridfs");

// Load environment variables
dotenv.config();

// Create Express app
const app = express();

// Global middleware
app.use(
  cors({
    origin: "*",
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);
app.use(express.json());

// Initialize connections (will be cached for subsequent requests)
const initializeConnections = async () => {
  try {
    // Connect to MongoDB
    await connectDB();
    
    // Connect to GridFS after MongoDB connection is established
    try {
      await connectGridFS();
    } catch (err) {
      console.error("Failed to connect to GridFS:", err);
      console.log("Continuing without GridFS...");
    }

    // Connect to Redis
    try {
      await connectRedis();
    } catch (err) {
      console.error("Failed to connect to Redis:", err);
      console.log("Continuing without Redis caching...");
    }
  } catch (error) {
    console.error("Failed to initialize connections:", error);
    // Don't throw error, let the app continue
  }
};

// Middleware to ensure database connection
app.use(async (req, res, next) => {
  try {
    await ensureConnection();
    next();
  } catch (error) {
    console.error("Database connection error:", error);
    res.status(500).json({
      status: "error",
      message: "Database connection failed"
    });
  }
});

// Import routes
const authRoutes = require("../routes/authRoutes");
const userRoutes = require("../routes/userRoutes");
const productRouter = require("../routes/gridfsProductRoutes");
const orderRouter = require("../routes/orderRoutes");
const contactRoutes = require("../routes/contactRoutes");
const imageRoutes = require("../routes/imageRoutes");
const videoBlogRoutes = require("../routes/videoBlogRoutes");

// Swagger API Documentation
app.use(
  "/api-docs",
  swaggerUi.serve,
  swaggerUi.setup(swaggerSpecs, { explorer: true })
);

// Root route - redirect to API docs
app.get("/", (_, res) => {
  res.json({
    message: "Chinioti Wooden Art API is Healthy",
    status: "success",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
    documentation: `${process.env.DEPLOYED_URL || 'https://your-app.vercel.app'}/api-docs`,
  });
});

// Auth callback fallback route
app.get("/auth-callback", (req, res) => {
  const { token, error, errorMessage } = req.query;

  if (error) {
    res.status(400).json({
      status: "error",
      message: errorMessage || "Authentication failed",
    });
  } else if (token) {
    res.json({
      status: "success",
      message: "Authentication successful",
      token,
      note: "This is the backend auth-callback endpoint. You should be redirected to the frontend.",
    });
  } else {
    res.status(400).json({
      status: "error",
      message: "Invalid auth callback request",
    });
  }
});

// API routes
app.use("/api/auth", authRoutes);
app.use("/api/users", userRoutes);
app.use("/api/products", productRouter);
app.use("/api/orders", orderRouter);
app.use("/api/contact", contactRoutes);
app.use("/api/images", imageRoutes);
app.use("/api/video-blogs", videoBlogRoutes);

// Initialize connections on first request
let initialized = false;
app.use(async (req, res, next) => {
  if (!initialized) {
    await initializeConnections();
    initialized = true;
  }
  next();
});

// Export the Express app for Vercel
module.exports = app;
