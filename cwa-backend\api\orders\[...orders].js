const { ensureConnection } = require("../../config/db");
const OrderController = require("../../controllers/orderController");

module.exports = async (req, res) => {
  try {
    // Ensure database connection
    await ensureConnection();
    
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    
    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }
    
    // Parse the URL to get the path segments
    const url = new URL(req.url, `http://${req.headers.host}`);
    const pathSegments = url.pathname.split('/').filter(segment => segment);

    // For catch-all routes in Vercel, we need to find the segments after 'orders'
    const ordersIndex = pathSegments.findIndex(segment => segment === 'orders');
    const orderId = ordersIndex >= 0 && pathSegments[ordersIndex + 1] ? pathSegments[ordersIndex + 1] : null;
    
    console.log('Orders catch-all API - URL:', req.url);
    console.log('Orders catch-all API - Path segments:', pathSegments);
    console.log('Orders catch-all API - ordersIndex:', ordersIndex, 'orderId:', orderId);
    
    if (orderId) {
      // Handle individual order operations
      req.params = { id: orderId };
      
      if (req.method === 'GET') {
        // GET /api/orders/:id
        return await OrderController.getOrder(req, res);
      } else if (req.method === 'PATCH') {
        // PATCH /api/orders/:id
        return await OrderController.updateOrder(req, res);
      } else if (req.method === 'DELETE') {
        // DELETE /api/orders/:id
        return await OrderController.deleteOrder(req, res);
      } else {
        return res.status(405).json({
          status: "error",
          message: "Method not allowed for individual order"
        });
      }
    } else {
      // Handle collection operations
      if (req.method === 'POST') {
        // POST /api/orders
        return await OrderController.createOrder(req, res);
      } else if (req.method === 'GET') {
        // GET /api/orders
        return await OrderController.getAllOrders(req, res);
      } else {
        return res.status(405).json({
          status: "error",
          message: "Method not allowed"
        });
      }
    }
    
  } catch (error) {
    console.error("Orders catch-all API error:", error);
    return res.status(500).json({
      status: "error",
      message: "Internal server error",
      error: error.message
    });
  }
};
