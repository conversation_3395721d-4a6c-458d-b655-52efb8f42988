const { ensureConnection } = require("../../config/db");
const ProductController = require("../../controllers/gridfsProductController");
const { uploadProductImages } = require("../../middleware/gridfsUploadMiddleware");

// Helper function to apply middleware
const applyMiddleware = (middleware, req, res) => {
  return new Promise((resolve, reject) => {
    middleware(req, res, (err) => {
      if (err) reject(err);
      else resolve();
    });
  });
};

module.exports = async (req, res) => {
  try {
    // Ensure database connection
    await ensureConnection();
    
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    
    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }
    
    // Parse the URL to get the path segments
    const url = new URL(req.url, `http://${req.headers.host}`);
    const pathSegments = url.pathname.split('/').filter(segment => segment);

    // For catch-all routes in Vercel, we need to find the segments after 'products'
    const productsIndex = pathSegments.findIndex(segment => segment === 'products');
    const productId = productsIndex >= 0 && pathSegments[productsIndex + 1] ? pathSegments[productsIndex + 1] : null;
    
    console.log('Products catch-all API - URL:', req.url);
    console.log('Products catch-all API - Path segments:', pathSegments);
    console.log('Products catch-all API - productsIndex:', productsIndex, 'productId:', productId);
    
    if (productId) {
      // Handle individual product operations
      req.params = { id: productId };
      
      if (req.method === 'GET') {
        // GET /api/products/:id
        return await ProductController.getProduct(req, res);
      } else if (req.method === 'PATCH') {
        // PATCH /api/products/:id
        try {
          await applyMiddleware(uploadProductImages, req, res);
          return await ProductController.updateProduct(req, res);
        } catch (uploadError) {
          console.error("Upload middleware error:", uploadError);
          return res.status(400).json({
            status: "error",
            message: "File upload failed",
            error: uploadError.message
          });
        }
      } else if (req.method === 'DELETE') {
        // DELETE /api/products/:id
        return await ProductController.deleteProduct(req, res);
      } else {
        return res.status(405).json({
          status: "error",
          message: "Method not allowed for individual product"
        });
      }
    } else {
      // Handle collection operations
      if (req.method === 'POST') {
        // POST /api/products - Create product
        try {
          await applyMiddleware(uploadProductImages, req, res);
          return await ProductController.createProduct(req, res);
        } catch (uploadError) {
          console.error("Upload middleware error:", uploadError);
          return res.status(400).json({
            status: "error",
            message: "File upload failed",
            error: uploadError.message
          });
        }
      } else if (req.method === 'GET') {
        // GET /api/products
        return await ProductController.getAllProducts(req, res);
      } else {
        return res.status(405).json({
          status: "error",
          message: "Method not allowed"
        });
      }
    }
    
  } catch (error) {
    console.error("Products catch-all API error:", error);
    return res.status(500).json({
      status: "error",
      message: "Internal server error",
      error: error.message
    });
  }
};
