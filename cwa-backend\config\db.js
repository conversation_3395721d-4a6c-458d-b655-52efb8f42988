const mongoose = require('mongoose');

// Global variable to track connection state
let isConnecting = false;
let connectionPromise = null;

const connectDB = async () => {
    // If already connected, return immediately
    if (mongoose.connection.readyState === 1) {
        console.log('Using existing MongoDB connection');
        return mongoose.connection;
    }

    // If currently connecting, wait for the existing connection attempt
    if (isConnecting && connectionPromise) {
        console.log('Waiting for existing connection attempt...');
        return await connectionPromise;
    }

    // If disconnected or uninitialized, create new connection
    if (mongoose.connection.readyState === 0 || mongoose.connection.readyState === 3) {
        isConnecting = true;

        try {
            console.log('Establishing new MongoDB connection...');

            // Configure mongoose for serverless environment
            mongoose.set('bufferCommands', false);
            mongoose.set('strictQuery', false);

            // Optimized connection options for Vercel serverless
            const connectionOptions = {
                maxPoolSize: 5, // Reduced pool size for serverless
                serverSelectionTimeoutMS: 10000, // Increased timeout for cold starts
                socketTimeoutMS: 30000, // Reduced socket timeout
                connectTimeoutMS: 10000, // Connection timeout
                maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
                retryWrites: true,
                w: 'majority'
            };

            connectionPromise = mongoose.connect(process.env.MONGO_URI, connectionOptions);
            const connection = await connectionPromise;

            console.log('MongoDB connected successfully');
            console.log(`Connected to database: ${mongoose.connection.name}`);

            isConnecting = false;
            return connection;
        } catch (error) {
            isConnecting = false;
            connectionPromise = null;
            console.error('MongoDB connection error:', error);

            // Log additional debugging information
            console.error('Connection readyState:', mongoose.connection.readyState);
            console.error('MONGO_URI exists:', !!process.env.MONGO_URI);
            console.error('MONGO_URI format:', process.env.MONGO_URI ? 'Valid format' : 'Missing');

            throw new Error(`Database connection failed: ${error.message}`);
        }
    }

    return mongoose.connection;
};

// Function to ensure database connection before operations
const ensureConnection = async () => {
    try {
        if (mongoose.connection.readyState !== 1) {
            console.log('Database not connected, attempting to connect...');
            await connectDB();
        }
        return true;
    } catch (error) {
        console.error('Failed to ensure database connection:', error);
        return false;
    }
};

// Function to get connection status
const getConnectionStatus = () => {
    const states = {
        0: 'disconnected',
        1: 'connected',
        2: 'connecting',
        3: 'disconnecting'
    };

    return {
        state: mongoose.connection.readyState,
        status: states[mongoose.connection.readyState] || 'unknown',
        name: mongoose.connection.name || null,
        host: mongoose.connection.host || null
    };
};

// Function to test database connection
const testConnection = async () => {
    try {
        await ensureConnection();

        if (mongoose.connection.readyState === 1) {
            // Test with a simple ping
            await mongoose.connection.db.admin().ping();
            return { success: true, message: 'Database connection test successful' };
        } else {
            return { success: false, message: 'Database not connected' };
        }
    } catch (error) {
        console.error('Database connection test failed:', error);
        return { success: false, message: error.message };
    }
};

module.exports = {
    connectDB,
    ensureConnection,
    getConnectionStatus,
    testConnection
};