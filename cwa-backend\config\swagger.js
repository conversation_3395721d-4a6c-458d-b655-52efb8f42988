const swaggerJsdoc = require("swagger-jsdoc");

const options = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "Chinioti Wooden Art API",
      version: "1.0.0",
      description:
        "API documentation for Chinioti Wooden Art e-commerce platform",
      contact: {
        name: "API Support",
        email: "<EMAIL>",
      },
    },
    servers: [
      {
        url: `${process.env.DEPLOYED_URL}/api`,
        description: "Development server",
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: ["./routes/*.js", "./models/*.js", "./swagger/*.js"],
};

const specs = swaggerJsdoc(options);

module.exports = specs;
