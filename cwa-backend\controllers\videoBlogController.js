const VideoBlog = require('../models/VideoBlog');
const { clearCache } = require('../config/redis');
const mongoose = require('mongoose');

/**
 * Create a new video blog
 * @route POST /api/video-blogs
 * @access Public
 */
const createVideoBlog = async (req, res) => {
  try {
    const {
      title,
      description,
      videoFileId,
      videoFilename,
      mimetype,
      fileSize,
      duration,
      thumbnailFileId,
      thumbnailFilename,
      videoUrl,
      thumbnailUrl,
      youtubeUrl,
      youtubeVideoId,
      category,
      tags
    } = req.body;

    // Validate required fields
    if (!title) {
      return res.status(400).json({
        status: 'error',
        message: 'Title is required'
      });
    }

    if (!description) {
      return res.status(400).json({
        status: 'error',
        message: 'Description is required'
      });
    }

    if (!videoUrl) {
      return res.status(400).json({
        status: 'error',
        message: 'Video URL is required'
      });
    }

    if (!thumbnailUrl) {
      return res.status(400).json({
        status: 'error',
        message: 'Thumbnail URL is required'
      });
    }

    // Determine if this is a YouTube video or file-based video
    const isYouTubeVideo = youtubeVideoId || (youtubeUrl && youtubeUrl.includes('youtube.com' || youtubeUrl.includes('youtu.be')));

    // Validate YouTube-specific fields
    if (isYouTubeVideo) {
      if (!youtubeVideoId) {
        return res.status(400).json({
          status: 'error',
          message: 'YouTube video ID is required for YouTube videos'
        });
      }
    } else {
      // Validate file-based video fields
      if (!videoFileId) {
        return res.status(400).json({
          status: 'error',
          message: 'Video file ID is required for file-based videos'
        });
      }

      if (!videoFilename) {
        return res.status(400).json({
          status: 'error',
          message: 'Video filename is required for file-based videos'
        });
      }

      if (!mimetype) {
        return res.status(400).json({
          status: 'error',
          message: 'Video mimetype is required for file-based videos'
        });
      }

      if (!fileSize || fileSize <= 0) {
        return res.status(400).json({
          status: 'error',
          message: 'Valid file size is required for file-based videos'
        });
      }
    }

    // Create video blog document
    const videoBlogData = {
      title: title.trim(),
      description: description.trim(),
      videoUrl: videoUrl.trim(),
      thumbnailUrl: thumbnailUrl.trim(),
      category: category ? category.trim() : 'General',
      tags: Array.isArray(tags) ? tags.map(tag => tag.trim()).filter(tag => tag.length > 0) : []
    };

    // Add file-based video fields if provided
    if (videoFileId) {
      videoBlogData.videoFileId = videoFileId.trim();
    }
    if (videoFilename) {
      videoBlogData.videoFilename = videoFilename.trim();
    }
    if (mimetype) {
      videoBlogData.mimetype = mimetype.trim();
    }
    if (fileSize) {
      videoBlogData.fileSize = parseInt(fileSize);
    }

    // Add YouTube-specific fields if provided
    if (youtubeUrl) {
      videoBlogData.youtubeUrl = youtubeUrl.trim();
    }
    if (youtubeVideoId) {
      videoBlogData.youtubeVideoId = youtubeVideoId.trim();
    }

    // Add optional fields
    if (duration && duration > 0) {
      videoBlogData.duration = parseInt(duration);
    }

    if (thumbnailFileId) {
      videoBlogData.thumbnailFileId = thumbnailFileId.trim();
    }

    if (thumbnailFilename) {
      videoBlogData.thumbnailFilename = thumbnailFilename.trim();
    }

    const videoBlog = new VideoBlog(videoBlogData);
    await videoBlog.save();

    // Clear cache
    await clearCache('video-blogs:*');

    res.status(201).json({
      status: 'success',
      message: 'Video blog created successfully',
      data: {
        videoBlog
      }
    });

  } catch (error) {
    console.error('Error creating video blog:', error);

    res.status(500).json({
      status: 'error',
      message: 'Failed to create video blog',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get all video blogs with pagination
 * @route GET /api/video-blogs
 * @access Public
 */
const getAllVideoBlogs = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const skip = (page - 1) * limit;

    // Build query
    const query = { isActive: true };

    // Add search functionality
    if (req.query.search) {
      query.$text = { $search: req.query.search };
    }

    // Add category filter
    if (req.query.category && req.query.category !== 'all') {
      query.category = { $regex: new RegExp(req.query.category, 'i') };
    }

    // Add tag filter
    if (req.query.tag) {
      query.tags = { $in: [new RegExp(req.query.tag, 'i')] };
    }

    // Get total count for pagination
    const total = await VideoBlog.countDocuments(query);

    // Fetch video blogs
    const videoBlogs = await VideoBlog.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.status(200).json({
      status: 'success',
      data: {
        videoBlogs,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: total,
          itemsPerPage: limit,
          hasNextPage,
          hasPrevPage
        }
      }
    });

  } catch (error) {
    console.error('Error fetching video blogs:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch video blogs',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get a single video blog by ID
 * @route GET /api/video-blogs/:id
 * @access Public
 */
const getVideoBlogById = async (req, res) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid video blog ID'
      });
    }

    const videoBlog = await VideoBlog.findOne({
      _id: id,
      isActive: true
    });

    if (!videoBlog) {
      return res.status(404).json({
        status: 'error',
        message: 'Video blog not found'
      });
    }

    // Increment view count
    videoBlog.views += 1;
    await videoBlog.save();

    res.status(200).json({
      status: 'success',
      data: {
        videoBlog
      }
    });

  } catch (error) {
    console.error('Error fetching video blog:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch video blog',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};



/**
 * Update video blog
 * @route PATCH /api/video-blogs/:id
 * @access Public
 */
const updateVideoBlog = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      description,
      isActive,
      videoUrl,
      thumbnailUrl,
      duration,
      videoFileId,
      videoFilename,
      mimetype,
      fileSize,
      thumbnailFileId,
      thumbnailFilename,
      youtubeUrl,
      youtubeVideoId,
      category,
      tags
    } = req.body;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid video blog ID'
      });
    }

    const videoBlog = await VideoBlog.findById(id);

    if (!videoBlog) {
      return res.status(404).json({
        status: 'error',
        message: 'Video blog not found'
      });
    }

    // Update fields
    if (title !== undefined) {
      videoBlog.title = title.trim();
    }
    if (description !== undefined) {
      videoBlog.description = description.trim();
    }
    if (isActive !== undefined) {
      videoBlog.isActive = isActive;
    }
    if (videoUrl !== undefined) {
      videoBlog.videoUrl = videoUrl.trim();
    }
    if (thumbnailUrl !== undefined) {
      videoBlog.thumbnailUrl = thumbnailUrl.trim();
    }
    if (duration !== undefined) {
      videoBlog.duration = duration > 0 ? parseInt(duration) : null;
    }
    if (videoFileId !== undefined) {
      videoBlog.videoFileId = videoFileId ? videoFileId.trim() : null;
    }
    if (videoFilename !== undefined) {
      videoBlog.videoFilename = videoFilename ? videoFilename.trim() : null;
    }
    if (mimetype !== undefined) {
      videoBlog.mimetype = mimetype ? mimetype.trim() : null;
    }
    if (fileSize !== undefined) {
      videoBlog.fileSize = fileSize ? parseInt(fileSize) : null;
    }
    if (thumbnailFileId !== undefined) {
      videoBlog.thumbnailFileId = thumbnailFileId ? thumbnailFileId.trim() : null;
    }
    if (thumbnailFilename !== undefined) {
      videoBlog.thumbnailFilename = thumbnailFilename ? thumbnailFilename.trim() : null;
    }
    if (youtubeUrl !== undefined) {
      videoBlog.youtubeUrl = youtubeUrl ? youtubeUrl.trim() : null;
    }
    if (youtubeVideoId !== undefined) {
      videoBlog.youtubeVideoId = youtubeVideoId ? youtubeVideoId.trim() : null;
    }
    if (category !== undefined) {
      videoBlog.category = category ? category.trim() : 'General';
    }
    if (tags !== undefined) {
      videoBlog.tags = Array.isArray(tags) ? tags.map(tag => tag.trim()).filter(tag => tag.length > 0) : [];
    }

    await videoBlog.save();

    // Clear cache
    await clearCache('video-blogs:*');

    res.status(200).json({
      status: 'success',
      message: 'Video blog updated successfully',
      data: {
        videoBlog
      }
    });

  } catch (error) {
    console.error('Error updating video blog:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to update video blog',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Delete video blog
 * @route DELETE /api/video-blogs/:id
 * @access Public
 */
const deleteVideoBlog = async (req, res) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid video blog ID'
      });
    }

    const videoBlog = await VideoBlog.findById(id);

    if (!videoBlog) {
      return res.status(404).json({
        status: 'error',
        message: 'Video blog not found'
      });
    }

    // Delete video blog document
    await VideoBlog.findByIdAndDelete(id);

    // Clear cache
    await clearCache('video-blogs:*');

    res.status(200).json({
      status: 'success',
      message: 'Video blog deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting video blog:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to delete video blog',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  createVideoBlog,
  getAllVideoBlogs,
  getVideoBlogById,
  updateVideoBlog,
  deleteVideoBlog
};
