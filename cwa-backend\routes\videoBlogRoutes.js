const express = require('express');
const router = express.Router();
const VideoBlogController = require('../controllers/videoBlogController');
const { cacheMiddleware } = require('../config/redis');

/**
 * @swagger
 * components:
 *   schemas:
 *     VideoBlog:
 *       type: object
 *       required:
 *         - title
 *         - videoFileId
 *         - videoFilename
 *         - mimetype
 *         - fileSize
 *         - videoUrl
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the video blog
 *         title:
 *           type: string
 *           description: The title of the video blog
 *           maxLength: 200
 *         videoFileId:
 *           type: string
 *           description: Unique identifier for the video
 *         videoFilename:
 *           type: string
 *           description: Original filename of the video
 *         mimetype:
 *           type: string
 *           description: MIME type of the video file
 *         fileSize:
 *           type: number
 *           description: Size of the video file in bytes
 *         duration:
 *           type: number
 *           description: Duration of the video in seconds
 *         thumbnailFileId:
 *           type: string
 *           description: Unique identifier for the thumbnail image
 *         thumbnailFilename:
 *           type: string
 *           description: Original filename of the thumbnail
 *         isActive:
 *           type: boolean
 *           description: Whether the video blog is active
 *           default: true
 *         views:
 *           type: number
 *           description: Number of views
 *           default: 0
 *         videoUrl:
 *           type: string
 *           description: Direct URL to the video file
 *         thumbnailUrl:
 *           type: string
 *           description: Direct URL to the thumbnail image
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 */

/**
 * @swagger
 * /api/video-blogs:
 *   post:
 *     summary: Create a new video blog
 *     tags: [Video Blogs]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - videoFileId
 *               - videoFilename
 *               - mimetype
 *               - fileSize
 *               - videoUrl
 *             properties:
 *               title:
 *                 type: string
 *                 description: Title of the video blog
 *                 maxLength: 200
 *               videoFileId:
 *                 type: string
 *                 description: Unique identifier for the video
 *               videoFilename:
 *                 type: string
 *                 description: Original filename of the video
 *               mimetype:
 *                 type: string
 *                 description: MIME type of the video file
 *               fileSize:
 *                 type: number
 *                 description: Size of the video file in bytes
 *               duration:
 *                 type: number
 *                 description: Duration of the video in seconds
 *               thumbnailFileId:
 *                 type: string
 *                 description: Unique identifier for the thumbnail
 *               thumbnailFilename:
 *                 type: string
 *                 description: Original filename of the thumbnail
 *               videoUrl:
 *                 type: string
 *                 description: Direct URL to the video file
 *               thumbnailUrl:
 *                 type: string
 *                 description: Direct URL to the thumbnail image
 *     responses:
 *       201:
 *         description: Video blog created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Video blog created successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     videoBlog:
 *                       $ref: '#/components/schemas/VideoBlog'
 *       400:
 *         description: Bad request - validation error
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/', VideoBlogController.createVideoBlog);

/**
 * @swagger
 * /api/video-blogs:
 *   get:
 *     summary: Get all video blogs with pagination
 *     tags: [Video Blogs]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 12
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for video titles
 *     responses:
 *       200:
 *         description: List of video blogs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     videoBlogs:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/VideoBlog'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         currentPage:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *                         totalItems:
 *                           type: integer
 *                         itemsPerPage:
 *                           type: integer
 *                         hasNextPage:
 *                           type: boolean
 *                         hasPrevPage:
 *                           type: boolean
 *       500:
 *         description: Server error
 */
router.get('/', cacheMiddleware(300), VideoBlogController.getAllVideoBlogs);

/**
 * @swagger
 * /api/video-blogs/{id}:
 *   get:
 *     summary: Get a video blog by ID
 *     tags: [Video Blogs]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Video blog ID
 *     responses:
 *       200:
 *         description: Video blog retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     videoBlog:
 *                       $ref: '#/components/schemas/VideoBlog'
 *       400:
 *         description: Invalid video blog ID
 *       404:
 *         description: Video blog not found
 *       500:
 *         description: Server error
 */
router.get('/:id', cacheMiddleware(300), VideoBlogController.getVideoBlogById);



/**
 * @swagger
 * /api/video-blogs/{id}:
 *   patch:
 *     summary: Update a video blog
 *     tags: [Video Blogs]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Video blog ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 maxLength: 200
 *               isActive:
 *                 type: boolean
 *               videoUrl:
 *                 type: string
 *                 description: Direct URL to the video file
 *               thumbnailUrl:
 *                 type: string
 *                 description: Direct URL to the thumbnail image
 *               duration:
 *                 type: number
 *                 description: Duration of the video in seconds
 *               videoFileId:
 *                 type: string
 *                 description: Unique identifier for the video
 *               videoFilename:
 *                 type: string
 *                 description: Original filename of the video
 *               mimetype:
 *                 type: string
 *                 description: MIME type of the video file
 *               fileSize:
 *                 type: number
 *                 description: Size of the video file in bytes
 *               thumbnailFileId:
 *                 type: string
 *                 description: Unique identifier for the thumbnail
 *               thumbnailFilename:
 *                 type: string
 *                 description: Original filename of the thumbnail
 *     responses:
 *       200:
 *         description: Video blog updated successfully
 *       400:
 *         description: Invalid video blog ID or validation error
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Video blog not found
 *       500:
 *         description: Server error
 */
router.patch('/:id', VideoBlogController.updateVideoBlog);

/**
 * @swagger
 * /api/video-blogs/{id}:
 *   delete:
 *     summary: Delete a video blog
 *     tags: [Video Blogs]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Video blog ID
 *     responses:
 *       200:
 *         description: Video blog deleted successfully
 *       400:
 *         description: Invalid video blog ID
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Video blog not found
 *       500:
 *         description: Server error
 */
router.delete('/:id', VideoBlogController.deleteVideoBlog);

module.exports = router;
