{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/apis/youtubeBlogs.ts"], "sourcesContent": ["// Types for YouTube blog data\nexport interface YouTubeBlogData {\n  id: string;\n  title: string;\n  description: string;\n  youtubeUrl: string;\n  youtubeVideoId: string;\n  thumbnailUrl: string;\n  category: string;\n  tags: string[];\n  isActive: boolean;\n  views: number;\n  createdAt: string;\n  updatedAt: string;\n  author?: {\n    id: string;\n    name: string;\n    email: string;\n  };\n}\n\nexport interface YouTubeBlogPagination {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  hasNextPage: boolean;\n  hasPrevPage: boolean;\n}\n\nexport interface YouTubeBlogResponse {\n  status: string;\n  data: {\n    blogs: YouTubeBlogData[];\n    pagination: YouTubeBlogPagination;\n  };\n}\n\n// Sample data for YouTube blogs (replace with actual API calls later)\nconst sampleYouTubeBlogs: YouTubeBlogData[] = [\n  {\n    id: \"1\",\n    title: \"Traditional Chinioti Wood Carving Techniques\",\n    description: \"Watch master craftsmen demonstrate the ancient art of Chinioti wood carving, passed down through generations.\",\n    youtubeUrl: \"https://www.youtube.com/shorts/yDiserKDRVo\",\n    youtubeVideoId: \"yDiserKDRVo\",\n    thumbnailUrl: \"https://i.ytimg.com/vi/yDiserKDRVo/oar2.jpg?sqp=-oaymwEoCJUDENAFSFqQAgHyq4qpAxcIARUAAIhC2AEB4gEKCBgQAhgGOAFAAQ==&rs=AOn4CLDf5TSXc0EUGvkZaDsqYiCJ32v-1w\",\n    category: \"Craftsmanship\",\n    tags: [\"woodworking\", \"traditional\", \"carving\", \"chinioti\"],\n    isActive: true,\n    views: 1250,\n    createdAt: \"2024-01-15T10:00:00Z\",\n    updatedAt: \"2024-01-15T10:00:00Z\",\n    author: {\n      id: \"1\",\n      name: \"Hassan Ali\",\n      email: \"<EMAIL>\"\n    }\n  },\n  {\n    id: \"2\",\n    title: \"Behind the Scenes: Creating a Chinioti Dining Set\",\n    description: \"Follow the complete process of creating a beautiful Chinioti dining set from raw wood to finished masterpiece.\",\n    youtubeUrl: \"https://www.youtube.com/shorts/CyDlZBn7T00\",\n    youtubeVideoId: \"CyDlZBn7T00\",\n    thumbnailUrl: \"https://i.ytimg.com/vi/CyDlZBn7T00/oar2.jpg?sqp=-oaymwEoCJUDENAFSFqQAgHyq4qpAxcIARUAAIhC2AEB4gEKCBgQAhgGOAFAAQ==&rs=AOn4CLDf37RSYPpkZaiVvUiXijvXZoiT8A\",\n    category: \"Craftsmanship\",\n    tags: [\"dining set\", \"furniture\", \"process\", \"handmade\"],\n    isActive: true,\n    views: 890,\n    createdAt: \"2024-01-10T14:30:00Z\",\n    updatedAt: \"2024-01-10T14:30:00Z\",\n    author: {\n      id: \"2\",\n      name: \"Hassan Ali\",\n      email: \"<EMAIL>\"\n    }\n  },\n  {\n    id: \"3\",\n    title: \"Wood Selection and Preparation for Fine Furniture\",\n    description: \"Learn about the different types of wood used in Chinioti furniture and how they are prepared for crafting.\",\n    youtubeUrl: \"https://www.youtube.com/shorts/MWfQXczPcAw\",\n    youtubeVideoId: \"MWfQXczPcAw\",\n    thumbnailUrl: \"https://i.ytimg.com/vi/MWfQXczPcAw/oar2.jpg?sqp=-oaymwEoCJUDENAFSFqQAgHyq4qpAxcIARUAAIhC2AEB4gEKCBgQAhgGOAFAAQ==&rs=AOn4CLBGY7yiPrToXMM21uCInKcHWJ1kJg\",\n    category: \"Craftsmanship\",\n    tags: [\"wood selection\", \"materials\", \"preparation\", \"quality\"],\n    isActive: true,\n    views: 567,\n    createdAt: \"2024-01-05T09:15:00Z\",\n    updatedAt: \"2024-01-05T09:15:00Z\",\n    author: {\n      id: \"1\",\n      name: \"Hassan Ali\",\n      email: \"<EMAIL>\"\n    }\n  },\n  {\n    id: \"4\",\n    title: \"Finishing Techniques for Wooden Furniture\",\n    description: \"Discover the various finishing techniques that give Chinioti furniture its distinctive look and protection.\",\n    youtubeUrl: \"https://www.youtube.com/shorts/46rOWwdXG2c\",\n    youtubeVideoId: \"46rOWwdXG2c\",\n    thumbnailUrl: \"https://i.ytimg.com/vi/46rOWwdXG2c/hq720_2.jpg?sqp=-oaymwEoCIIDEOADSFryq4qpAxoIARUAAIhC0AEB2AEB4gEKCBgQAhgGOAFAAQ==&rs=AOn4CLDMx_AzEv7lMnHlJoHqpCg7sjcL2w\",\n    category: \"Craftsmanship\",\n    tags: [\"finishing\", \"polish\", \"protection\", \"techniques\"],\n    isActive: true,\n    views: 423,\n    createdAt: \"2024-01-03T16:20:00Z\",\n    updatedAt: \"2024-01-03T16:20:00Z\",\n    author: {\n      id: \"2\",\n      name: \"Hassan Ali\",\n      email: \"<EMAIL>\"\n    }\n  },\n  {\n    id: \"5\",\n    title: \"Maintenance Tips for Wooden Furniture\",\n    description: \"Essential maintenance tips to keep your wooden furniture looking beautiful for years to come.\",\n    youtubeUrl: \"https://www.youtube.com/shorts/yzfPOALe86A\",\n    youtubeVideoId: \"yzfPOALe86A\",\n    thumbnailUrl: \"https://i.ytimg.com/vi/yzfPOALe86A/hq720_2.jpg?sqp=-oaymwEoCJUDENAFSFryq4qpAxoIARUAAIhC0AEB2AEB4gEKCBgQAhgGOAFAAQ==&rs=AOn4CLA4D-1Hvv9qIJwGpywKXRsdbo7BOg\",\n    category: \"Craftsmanship\",\n    tags: [\"maintenance\", \"care\", \"cleaning\", \"preservation\"],\n    isActive: true,\n    views: 789,\n    createdAt: \"2024-01-01T11:30:00Z\",\n    updatedAt: \"2024-01-01T11:30:00Z\",\n    author: {\n      id: \"1\",\n      name: \"Hassan Ali\",\n      email: \"<EMAIL>\"\n    }\n  }\n];\n\n/**\n * Extract YouTube video ID from URL\n */\nexport const extractYouTubeVideoId = (url: string): string | null => {\n  const regex = /(?:youtube\\.com\\/(?:[^\\/]+\\/.+\\/|(?:v|e(?:mbed)?)\\/|.*[?&]v=)|youtu\\.be\\/)([^\"&?\\/\\s]{11})/;\n  const match = url.match(regex);\n  return match ? match[1] : null;\n};\n\n/**\n * Get YouTube thumbnail URL\n */\nexport const getYouTubeThumbnail = (videoId: string, quality: 'default' | 'medium' | 'high' | 'maxres' = 'maxres'): string => {\n  return `https://img.youtube.com/vi/${videoId}/${quality}default.jpg`;\n};\n\n/**\n * Get YouTube embed URL\n */\nexport const getYouTubeEmbedUrl = (videoId: string): string => {\n  return `https://www.youtube.com/embed/${videoId}`;\n};\n\n/**\n * Get all YouTube blogs with pagination and optional search\n */\nexport const getYouTubeBlogs = async (\n  page: number = 1,\n  limit: number = 12,\n  search?: string,\n  category?: string\n): Promise<{ blogs: YouTubeBlogData[]; pagination: YouTubeBlogPagination }> => {\n  try {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    \n    let filteredBlogs = [...sampleYouTubeBlogs];\n    \n    // Apply search filter\n    if (search) {\n      const searchLower = search.toLowerCase();\n      filteredBlogs = filteredBlogs.filter(blog => \n        blog.title.toLowerCase().includes(searchLower) ||\n        blog.description.toLowerCase().includes(searchLower) ||\n        blog.tags.some(tag => tag.toLowerCase().includes(searchLower))\n      );\n    }\n    \n    // Apply category filter\n    if (category && category !== 'all') {\n      filteredBlogs = filteredBlogs.filter(blog => \n        blog.category.toLowerCase() === category.toLowerCase()\n      );\n    }\n    \n    // Calculate pagination\n    const totalItems = filteredBlogs.length;\n    const totalPages = Math.ceil(totalItems / limit);\n    const startIndex = (page - 1) * limit;\n    const endIndex = startIndex + limit;\n    const paginatedBlogs = filteredBlogs.slice(startIndex, endIndex);\n    \n    return {\n      blogs: paginatedBlogs,\n      pagination: {\n        currentPage: page,\n        totalPages,\n        totalItems,\n        itemsPerPage: limit,\n        hasNextPage: page < totalPages,\n        hasPrevPage: page > 1\n      }\n    };\n  } catch (error) {\n    console.error(\"Error fetching YouTube blogs:\", error);\n    return {\n      blogs: [],\n      pagination: {\n        currentPage: 1,\n        totalPages: 0,\n        totalItems: 0,\n        itemsPerPage: limit,\n        hasNextPage: false,\n        hasPrevPage: false\n      }\n    };\n  }\n};\n\n/**\n * Get a single YouTube blog by ID\n */\nexport const getYouTubeBlogById = async (id: string): Promise<YouTubeBlogData | undefined> => {\n  try {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 300));\n    \n    return sampleYouTubeBlogs.find(blog => blog.id === id);\n  } catch (error) {\n    console.error(`Error fetching YouTube blog with ID ${id}:`, error);\n    return undefined;\n  }\n};\n\n/**\n * Get unique categories from YouTube blogs\n */\nexport const getYouTubeBlogCategories = async (): Promise<string[]> => {\n  try {\n    const categories = [...new Set(sampleYouTubeBlogs.map(blog => blog.category))];\n    return categories;\n  } catch (error) {\n    console.error(\"Error fetching YouTube blog categories:\", error);\n    return [];\n  }\n};\n\n/**\n * Add a new YouTube blog (for admin use)\n */\nexport const addYouTubeBlog = async (blogData: Omit<YouTubeBlogData, 'id' | 'createdAt' | 'updatedAt' | 'views'>): Promise<YouTubeBlogData | null> => {\n  try {\n    // Extract video ID from URL\n    const videoId = extractYouTubeVideoId(blogData.youtubeUrl);\n    if (!videoId) {\n      throw new Error('Invalid YouTube URL');\n    }\n\n    const newBlog: YouTubeBlogData = {\n      ...blogData,\n      id: Date.now().toString(),\n      youtubeVideoId: videoId,\n      thumbnailUrl: getYouTubeThumbnail(videoId),\n      views: 0,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n\n    // In a real app, this would make an API call\n    sampleYouTubeBlogs.unshift(newBlog);\n\n    return newBlog;\n  } catch (error) {\n    console.error(\"Error adding YouTube blog:\", error);\n    return null;\n  }\n};\n\n/**\n * Helper function to quickly add a YouTube video blog\n * Usage: addQuickYouTubeBlog(\"https://www.youtube.com/watch?v=VIDEO_ID\", \"Video Title\", \"Video Description\", \"Category\")\n */\nexport const addQuickYouTubeBlog = async (\n  youtubeUrl: string,\n  title: string,\n  description: string,\n  category: string = \"General\",\n  tags: string[] = [],\n  author?: { id: string; name: string; email: string }\n): Promise<YouTubeBlogData | null> => {\n  const blogData = {\n    title,\n    description,\n    youtubeUrl,\n    youtubeVideoId: \"\", // Will be extracted automatically\n    thumbnailUrl: \"\", // Will be generated automatically\n    category,\n    tags,\n    isActive: true,\n    author: author || {\n      id: \"1\",\n      name: \"Admin\",\n      email: \"<EMAIL>\"\n    }\n  };\n\n  return await addYouTubeBlog(blogData);\n};\n\n// Example usage (you can call this function to add videos):\n// addQuickYouTubeBlog(\n//   \"https://www.youtube.com/watch?v=dQw4w9WgXcQ\",\n//   \"How to Care for Wooden Furniture\",\n//   \"Learn the best practices for maintaining your wooden furniture to ensure it lasts for generations.\",\n//   \"Care Tips\",\n//   [\"furniture\", \"care\", \"maintenance\", \"wood\"]\n// );\n"], "names": [], "mappings": "AAAA,8BAA8B;;;;;;;;;;;AAsC9B,sEAAsE;AACtE,MAAM,qBAAwC;IAC5C;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,YAAY;QACZ,gBAAgB;QAChB,cAAc;QACd,UAAU;QACV,MAAM;YAAC;YAAe;YAAe;YAAW;SAAW;QAC3D,UAAU;QACV,OAAO;QACP,WAAW;QACX,WAAW;QACX,QAAQ;YACN,IAAI;YACJ,MAAM;YACN,OAAO;QACT;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,YAAY;QACZ,gBAAgB;QAChB,cAAc;QACd,UAAU;QACV,MAAM;YAAC;YAAc;YAAa;YAAW;SAAW;QACxD,UAAU;QACV,OAAO;QACP,WAAW;QACX,WAAW;QACX,QAAQ;YACN,IAAI;YACJ,MAAM;YACN,OAAO;QACT;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,YAAY;QACZ,gBAAgB;QAChB,cAAc;QACd,UAAU;QACV,MAAM;YAAC;YAAkB;YAAa;YAAe;SAAU;QAC/D,UAAU;QACV,OAAO;QACP,WAAW;QACX,WAAW;QACX,QAAQ;YACN,IAAI;YACJ,MAAM;YACN,OAAO;QACT;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,YAAY;QACZ,gBAAgB;QAChB,cAAc;QACd,UAAU;QACV,MAAM;YAAC;YAAa;YAAU;YAAc;SAAa;QACzD,UAAU;QACV,OAAO;QACP,WAAW;QACX,WAAW;QACX,QAAQ;YACN,IAAI;YACJ,MAAM;YACN,OAAO;QACT;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,YAAY;QACZ,gBAAgB;QAChB,cAAc;QACd,UAAU;QACV,MAAM;YAAC;YAAe;YAAQ;YAAY;SAAe;QACzD,UAAU;QACV,OAAO;QACP,WAAW;QACX,WAAW;QACX,QAAQ;YACN,IAAI;YACJ,MAAM;YACN,OAAO;QACT;IACF;CACD;AAKM,MAAM,wBAAwB,CAAC;IACpC,MAAM,QAAQ;IACd,MAAM,QAAQ,IAAI,KAAK,CAAC;IACxB,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;AAC5B;AAKO,MAAM,sBAAsB,CAAC,SAAiB,UAAoD,QAAQ;IAC/G,OAAO,CAAC,2BAA2B,EAAE,QAAQ,CAAC,EAAE,QAAQ,WAAW,CAAC;AACtE;AAKO,MAAM,qBAAqB,CAAC;IACjC,OAAO,CAAC,8BAA8B,EAAE,SAAS;AACnD;AAKO,MAAM,kBAAkB,OAC7B,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,QACA;IAEA,IAAI;QACF,qBAAqB;QACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,IAAI,gBAAgB;eAAI;SAAmB;QAE3C,sBAAsB;QACtB,IAAI,QAAQ;YACV,MAAM,cAAc,OAAO,WAAW;YACtC,gBAAgB,cAAc,MAAM,CAAC,CAAA,OACnC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAClC,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACxC,KAAK,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC;QAErD;QAEA,wBAAwB;QACxB,IAAI,YAAY,aAAa,OAAO;YAClC,gBAAgB,cAAc,MAAM,CAAC,CAAA,OACnC,KAAK,QAAQ,CAAC,WAAW,OAAO,SAAS,WAAW;QAExD;QAEA,uBAAuB;QACvB,MAAM,aAAa,cAAc,MAAM;QACvC,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;QAC1C,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;QAChC,MAAM,WAAW,aAAa;QAC9B,MAAM,iBAAiB,cAAc,KAAK,CAAC,YAAY;QAEvD,OAAO;YACL,OAAO;YACP,YAAY;gBACV,aAAa;gBACb;gBACA;gBACA,cAAc;gBACd,aAAa,OAAO;gBACpB,aAAa,OAAO;YACtB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,OAAO,EAAE;YACT,YAAY;gBACV,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,cAAc;gBACd,aAAa;gBACb,aAAa;YACf;QACF;IACF;AACF;AAKO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,qBAAqB;QACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,OAAO,mBAAmB,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,GAAG,CAAC,CAAC,EAAE;QAC5D,OAAO;IACT;AACF;AAKO,MAAM,2BAA2B;IACtC,IAAI;QACF,MAAM,aAAa;eAAI,IAAI,IAAI,mBAAmB,GAAG,CAAC,CAAA,OAAQ,KAAK,QAAQ;SAAG;QAC9E,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO,EAAE;IACX;AACF;AAKO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,4BAA4B;QAC5B,MAAM,UAAU,sBAAsB,SAAS,UAAU;QACzD,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,UAA2B;YAC/B,GAAG,QAAQ;YACX,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,gBAAgB;YAChB,cAAc,oBAAoB;YAClC,OAAO;YACP,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,6CAA6C;QAC7C,mBAAmB,OAAO,CAAC;QAE3B,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAMO,MAAM,sBAAsB,OACjC,YACA,OACA,aACA,WAAmB,SAAS,EAC5B,OAAiB,EAAE,EACnB;IAEA,MAAM,WAAW;QACf;QACA;QACA;QACA,gBAAgB;QAChB,cAAc;QACd;QACA;QACA,UAAU;QACV,QAAQ,UAAU;YAChB,IAAI;YACJ,MAAM;YACN,OAAO;QACT;IACF;IAEA,OAAO,MAAM,eAAe;AAC9B,GAEA,4DAA4D;CAC5D,uBAAuB;CACvB,mDAAmD;CACnD,wCAAwC;CACxC,0GAA0G;CAC1G,iBAAiB;CACjB,iDAAiD;CACjD,KAAK", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/app/%28root%29/blogs/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport Link from \"next/link\";\r\nimport { useParams, useRouter } from \"next/navigation\";\r\nimport { ArrowLeft, Calendar, User, Eye, Share2, ExternalLink, Tag, SkipForward, SkipBack, Play, Pause } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { getYouTubeBlogById, getYouTubeEmbedUrl, getYouTubeBlogs, YouTubeBlogData } from \"@/apis/youtubeBlogs\";\r\nimport { toast } from \"sonner\";\r\n\r\n// YouTube API type declarations\r\ndeclare global {\r\n  interface Window {\r\n    YT: any;\r\n    onYouTubeIframeAPIReady: () => void;\r\n  }\r\n}\r\n\r\nconst BlogDetail = () => {\r\n  const params = useParams();\r\n  const router = useRouter();\r\n  const blogId = params.id as string;\r\n  const [blog, setBlog] = useState<YouTubeBlogData | null>(null);\r\n  const [allBlogs, setAllBlogs] = useState<YouTubeBlogData[]>([]);\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isAutoPlay, setIsAutoPlay] = useState(true);\r\n  const iframeRef = useRef<HTMLIFrameElement>(null);\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        // Fetch all blogs for playlist functionality\r\n        const blogsResult = await getYouTubeBlogs(1, 100); // Get all blogs\r\n        const blogs = blogsResult.blogs;\r\n        setAllBlogs(blogs);\r\n\r\n        // Find current blog and its index\r\n        const currentBlogIndex = blogs.findIndex(b => b.id === blogId);\r\n        if (currentBlogIndex !== -1) {\r\n          setBlog(blogs[currentBlogIndex]);\r\n          setCurrentIndex(currentBlogIndex);\r\n        } else {\r\n          // Fallback: try to fetch individual blog\r\n          const fetchedBlog = await getYouTubeBlogById(blogId);\r\n          if (fetchedBlog) {\r\n            setBlog(fetchedBlog);\r\n            setCurrentIndex(0);\r\n          } else {\r\n            toast.error(\"Blog not found\");\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching blog:\", error);\r\n        toast.error(\"Failed to load blog\");\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, [blogId]);\r\n\r\n  // Auto-play navigation functions\r\n  const goToNextVideo = () => {\r\n    if (allBlogs.length === 0) return;\r\n\r\n    const nextIndex = (currentIndex + 1) % allBlogs.length; // Loop back to start\r\n    const nextBlog = allBlogs[nextIndex];\r\n\r\n    setCurrentIndex(nextIndex);\r\n    setBlog(nextBlog);\r\n\r\n    // Show notification for auto-play\r\n    if (isAutoPlay) {\r\n      toast.success(`Auto-playing: ${nextBlog.title}`, {\r\n        duration: 3000,\r\n      });\r\n    }\r\n\r\n    router.push(`/blogs/${nextBlog.id}`);\r\n  };\r\n\r\n  const goToPreviousVideo = () => {\r\n    if (allBlogs.length === 0) return;\r\n\r\n    const prevIndex = currentIndex === 0 ? allBlogs.length - 1 : currentIndex - 1; // Loop to end\r\n    const prevBlog = allBlogs[prevIndex];\r\n\r\n    setCurrentIndex(prevIndex);\r\n    setBlog(prevBlog);\r\n    router.push(`/blogs/${prevBlog.id}`);\r\n  };\r\n\r\n  // YouTube iframe API integration for auto-play\r\n  useEffect(() => {\r\n    if (!blog || !isAutoPlay) return;\r\n\r\n    // Load YouTube IFrame API\r\n    const loadYouTubeAPI = () => {\r\n      if (window.YT) {\r\n        initializePlayer();\r\n        return;\r\n      }\r\n\r\n      const script = document.createElement('script');\r\n      script.src = 'https://www.youtube.com/iframe_api';\r\n      script.async = true;\r\n      document.body.appendChild(script);\r\n\r\n      window.onYouTubeIframeAPIReady = initializePlayer;\r\n    };\r\n\r\n    const initializePlayer = () => {\r\n      if (!window.YT || !iframeRef.current) return;\r\n\r\n      new window.YT.Player(iframeRef.current, {\r\n        events: {\r\n          onStateChange: (event: any) => {\r\n            // When video ends (state 0), play next video\r\n            if (event.data === window.YT.PlayerState.ENDED && isAutoPlay) {\r\n              setTimeout(() => {\r\n                goToNextVideo();\r\n              }, 1000); // 1 second delay before next video\r\n            }\r\n          }\r\n        }\r\n      });\r\n    };\r\n\r\n    loadYouTubeAPI();\r\n  }, [blog, isAutoPlay, currentIndex, allBlogs]);\r\n\r\n  const handleShare = async () => {\r\n    if (navigator.share && blog) {\r\n      try {\r\n        await navigator.share({\r\n          title: blog.title,\r\n          text: blog.description,\r\n          url: window.location.href,\r\n        });\r\n      } catch (error) {\r\n        // Fallback to copying URL\r\n        navigator.clipboard.writeText(window.location.href);\r\n        toast.success(\"Link copied to clipboard!\");\r\n      }\r\n    } else {\r\n      // Fallback to copying URL\r\n      navigator.clipboard.writeText(window.location.href);\r\n      toast.success(\"Link copied to clipboard!\");\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric'\r\n    });\r\n  };\r\n\r\n  const formatViews = (views: number) => {\r\n    if (views >= 1000000) {\r\n      return `${(views / 1000000).toFixed(1)}M`;\r\n    } else if (views >= 1000) {\r\n      return `${(views / 1000).toFixed(1)}K`;\r\n    }\r\n    return views.toString();\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-accent\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!blog) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Blog not found</h1>\r\n          <Link href=\"/blogs\">\r\n            <Button>\r\n              <ArrowLeft className=\"w-4 h-4 mr-2\" />\r\n              Back to Blogs\r\n            </Button>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      {/* Header */}\r\n      <div className=\"bg-white border-b\">\r\n        <div className=\"container mx-auto px-4 py-6\">\r\n          <Link href=\"/blogs\">\r\n            <Button variant=\"ghost\" className=\"mb-4\">\r\n              <ArrowLeft className=\"w-4 h-4 mr-2\" />\r\n              Back to Video Blogs\r\n            </Button>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <article className=\"container mx-auto px-4 py-8 max-w-4xl\">\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6 }}\r\n          className=\"bg-white rounded-lg shadow-lg overflow-hidden\"\r\n        >\r\n          {/* Video Player */}\r\n          <div className=\"relative\">\r\n            <div className=\"aspect-video bg-gray-100\">\r\n              <iframe\r\n                ref={iframeRef}\r\n                src={`${getYouTubeEmbedUrl(blog.youtubeVideoId)}?enablejsapi=1&autoplay=1`}\r\n                title={blog.title}\r\n                className=\"w-full h-full\"\r\n                style={{ border: 0 }}\r\n                allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\r\n                allowFullScreen\r\n              />\r\n            </div>\r\n\r\n            {/* Video Controls Overlay */}\r\n            <div className=\"absolute top-4 right-4 flex items-center gap-2\">\r\n              <Button\r\n                variant=\"secondary\"\r\n                size=\"sm\"\r\n                onClick={() => setIsAutoPlay(!isAutoPlay)}\r\n                className=\"bg-black/70 text-white hover:bg-black/80\"\r\n              >\r\n                {isAutoPlay ? <Pause className=\"w-4 h-4\" /> : <Play className=\"w-4 h-4\" />}\r\n                <span className=\"ml-1 text-xs\">{isAutoPlay ? 'Auto' : 'Manual'}</span>\r\n              </Button>\r\n            </div>\r\n\r\n            {/* Navigation Controls */}\r\n            {allBlogs.length > 1 && (\r\n              <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2\">\r\n                <Button\r\n                  variant=\"secondary\"\r\n                  size=\"sm\"\r\n                  onClick={goToPreviousVideo}\r\n                  className=\"bg-black/70 text-white hover:bg-black/80\"\r\n                >\r\n                  <SkipBack className=\"w-4 h-4\" />\r\n                </Button>\r\n\r\n                <div className=\"bg-black/70 text-white px-3 py-1 rounded text-sm\">\r\n                  {currentIndex + 1} / {allBlogs.length}\r\n                </div>\r\n\r\n                <Button\r\n                  variant=\"secondary\"\r\n                  size=\"sm\"\r\n                  onClick={goToNextVideo}\r\n                  className=\"bg-black/70 text-white hover:bg-black/80\"\r\n                >\r\n                  <SkipForward className=\"w-4 h-4\" />\r\n                </Button>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Content */}\r\n          <div className=\"p-6 md:p-8\">\r\n            {/* Header */}\r\n            <header className=\"mb-6\">\r\n              {/* Category */}\r\n              <div className=\"mb-3\">\r\n                <span className=\"bg-accent text-white text-sm font-medium px-3 py-1 rounded\">\r\n                  {blog.category}\r\n                </span>\r\n              </div>\r\n\r\n              {/* Title */}\r\n              <h1 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\r\n                {blog.title}\r\n              </h1>\r\n\r\n              {/* Meta information */}\r\n              <div className=\"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4\">\r\n                {blog.author && (\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <User className=\"w-4 h-4\" />\r\n                    <span>{blog.author.name}</span>\r\n                  </div>\r\n                )}\r\n                <div className=\"flex items-center gap-1\">\r\n                  <Calendar className=\"w-4 h-4\" />\r\n                  <span>{formatDate(blog.createdAt)}</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-1\">\r\n                  <Eye className=\"w-4 h-4\" />\r\n                  <span>{formatViews(blog.views)} views</span>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Action Buttons */}\r\n              <div className=\"flex flex-wrap items-center gap-3\">\r\n                <Button\r\n                  onClick={() => window.open(blog.youtubeUrl, '_blank')}\r\n                  className=\"flex items-center gap-2\"\r\n                >\r\n                  <ExternalLink className=\"w-4 h-4\" />\r\n                  Watch on YouTube\r\n                </Button>\r\n\r\n                <Button variant=\"outline\" onClick={handleShare}>\r\n                  <Share2 className=\"w-4 h-4 mr-2\" />\r\n                  Share Video\r\n                </Button>\r\n              </div>\r\n            </header>\r\n\r\n            {/* Description */}\r\n            <div className=\"prose prose-lg max-w-none mb-6\">\r\n              <p className=\"text-gray-700 leading-relaxed\">\r\n                {blog.description}\r\n              </p>\r\n            </div>\r\n\r\n            {/* Tags */}\r\n            {blog.tags && blog.tags.length > 0 && (\r\n              <div className=\"mb-6\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">Tags</h3>\r\n                <div className=\"flex flex-wrap gap-2\">\r\n                  {blog.tags.map((tag, index) => (\r\n                    <span\r\n                      key={index}\r\n                      className=\"inline-flex items-center gap-1 text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-full\"\r\n                    >\r\n                      <Tag className=\"w-3 h-3\" />\r\n                      {tag}\r\n                    </span>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Playlist Section */}\r\n            {allBlogs.length > 1 && (\r\n              <div className=\"mb-6\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\r\n                  Up Next ({allBlogs.length - 1} videos)\r\n                </h3>\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto\">\r\n                  {allBlogs\r\n                    .filter((_, index) => index !== currentIndex)\r\n                    .slice(0, 6) // Show max 6 upcoming videos\r\n                    .map((upcomingBlog) => (\r\n                      <div\r\n                        key={upcomingBlog.id}\r\n                        className=\"flex items-center gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors\"\r\n                        onClick={() => {\r\n                          const blogIndex = allBlogs.findIndex(b => b.id === upcomingBlog.id);\r\n                          setCurrentIndex(blogIndex);\r\n                          setBlog(upcomingBlog);\r\n                          router.push(`/blogs/${upcomingBlog.id}`);\r\n                        }}\r\n                      >\r\n                        <div className=\"relative w-20 h-12 bg-gray-200 rounded overflow-hidden flex-shrink-0\">\r\n                          <img\r\n                            src={upcomingBlog.thumbnailUrl}\r\n                            alt={upcomingBlog.title}\r\n                            className=\"w-full h-full object-cover\"\r\n                            onError={(e) => {\r\n                              const target = e.target as HTMLImageElement;\r\n                              target.style.display = 'none';\r\n                            }}\r\n                          />\r\n                          <div className=\"absolute inset-0 flex items-center justify-center\">\r\n                            <Play className=\"w-4 h-4 text-white drop-shadow-lg\" />\r\n                          </div>\r\n                        </div>\r\n                        <div className=\"flex-1 min-w-0\">\r\n                          <h4 className=\"text-sm font-medium text-gray-900 line-clamp-2\">\r\n                            {upcomingBlog.title}\r\n                          </h4>\r\n                          <p className=\"text-xs text-gray-500 mt-1\">\r\n                            {upcomingBlog.category} • {formatViews(upcomingBlog.views)} views\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Footer */}\r\n            <footer className=\"pt-6 border-t border-gray-200\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"text-sm text-gray-600\">\r\n                  Published on {formatDate(blog.createdAt)}\r\n                  {isAutoPlay && allBlogs.length > 1 && (\r\n                    <span className=\"ml-2 text-accent\">• Auto-play enabled</span>\r\n                  )}\r\n                </div>\r\n                <Link href=\"/blogs\">\r\n                  <Button variant=\"outline\">\r\n                    View All Videos\r\n                  </Button>\r\n                </Link>\r\n              </div>\r\n            </footer>\r\n          </div>\r\n        </motion.div>\r\n      </article>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BlogDetail;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AARA;;;;;;;;;;AAkBA,MAAM,aAAa;IACjB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,OAAO,EAAE;IACxB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,6CAA6C;gBAC7C,MAAM,cAAc,MAAM,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE,GAAG,MAAM,gBAAgB;gBACnE,MAAM,QAAQ,YAAY,KAAK;gBAC/B,YAAY;gBAEZ,kCAAkC;gBAClC,MAAM,mBAAmB,MAAM,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACvD,IAAI,qBAAqB,CAAC,GAAG;oBAC3B,QAAQ,KAAK,CAAC,iBAAiB;oBAC/B,gBAAgB;gBAClB,OAAO;oBACL,yCAAyC;oBACzC,MAAM,cAAc,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE;oBAC7C,IAAI,aAAa;wBACf,QAAQ;wBACR,gBAAgB;oBAClB,OAAO;wBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;KAAO;IAEX,iCAAiC;IACjC,MAAM,gBAAgB;QACpB,IAAI,SAAS,MAAM,KAAK,GAAG;QAE3B,MAAM,YAAY,CAAC,eAAe,CAAC,IAAI,SAAS,MAAM,EAAE,qBAAqB;QAC7E,MAAM,WAAW,QAAQ,CAAC,UAAU;QAEpC,gBAAgB;QAChB,QAAQ;QAER,kCAAkC;QAClC,IAAI,YAAY;YACd,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,SAAS,KAAK,EAAE,EAAE;gBAC/C,UAAU;YACZ;QACF;QAEA,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE;IACrC;IAEA,MAAM,oBAAoB;QACxB,IAAI,SAAS,MAAM,KAAK,GAAG;QAE3B,MAAM,YAAY,iBAAiB,IAAI,SAAS,MAAM,GAAG,IAAI,eAAe,GAAG,cAAc;QAC7F,MAAM,WAAW,QAAQ,CAAC,UAAU;QAEpC,gBAAgB;QAChB,QAAQ;QACR,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE;IACrC;IAEA,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ,CAAC,YAAY;QAE1B,0BAA0B;QAC1B,MAAM,iBAAiB;YACrB,IAAI,OAAO,EAAE,EAAE;gBACb;gBACA;YACF;YAEA,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,OAAO,GAAG,GAAG;YACb,OAAO,KAAK,GAAG;YACf,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,OAAO,uBAAuB,GAAG;QACnC;QAEA,MAAM,mBAAmB;YACvB,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,OAAO,EAAE;YAEtC,IAAI,OAAO,EAAE,CAAC,MAAM,CAAC,UAAU,OAAO,EAAE;gBACtC,QAAQ;oBACN,eAAe,CAAC;wBACd,6CAA6C;wBAC7C,IAAI,MAAM,IAAI,KAAK,OAAO,EAAE,CAAC,WAAW,CAAC,KAAK,IAAI,YAAY;4BAC5D,WAAW;gCACT;4BACF,GAAG,OAAO,mCAAmC;wBAC/C;oBACF;gBACF;YACF;QACF;QAEA;IACF,GAAG;QAAC;QAAM;QAAY;QAAc;KAAS;IAE7C,MAAM,cAAc;QAClB,IAAI,UAAU,KAAK,IAAI,MAAM;YAC3B,IAAI;gBACF,MAAM,UAAU,KAAK,CAAC;oBACpB,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,WAAW;oBACtB,KAAK,OAAO,QAAQ,CAAC,IAAI;gBAC3B;YACF,EAAE,OAAO,OAAO;gBACd,0BAA0B;gBAC1B,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,QAAQ,CAAC,IAAI;gBAClD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,OAAO;YACL,0BAA0B;YAC1B,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,QAAQ,CAAC,IAAI;YAClD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,SAAS,SAAS;YACpB,OAAO,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3C,OAAO,IAAI,SAAS,MAAM;YACxB,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACxC;QACA,OAAO,MAAM,QAAQ;IACvB;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,2HAAA,CAAA,SAAM;;8CACL,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;IAOlD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,WAAU;;8CAChC,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;0BAQ9C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,KAAK;wCACL,KAAK,GAAG,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,cAAc,EAAE,yBAAyB,CAAC;wCAC1E,OAAO,KAAK,KAAK;wCACjB,WAAU;wCACV,OAAO;4CAAE,QAAQ;wCAAE;wCACnB,OAAM;wCACN,eAAe;;;;;;;;;;;8CAKnB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;;4CAET,2BAAa,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;qEAAe,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAC9D,8OAAC;gDAAK,WAAU;0DAAgB,aAAa,SAAS;;;;;;;;;;;;;;;;;gCAKzD,SAAS,MAAM,GAAG,mBACjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,2HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAGtB,8OAAC;4CAAI,WAAU;;gDACZ,eAAe;gDAAE;gDAAI,SAAS,MAAM;;;;;;;sDAGvC,8OAAC,2HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAO/B,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAO,WAAU;;sDAEhB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,KAAK,QAAQ;;;;;;;;;;;sDAKlB,8OAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;sDAIb,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,MAAM,kBACV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;sEAAM,KAAK,MAAM,CAAC,IAAI;;;;;;;;;;;;8DAG3B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;sEAAM,WAAW,KAAK,SAAS;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,8OAAC;;gEAAM,YAAY,KAAK,KAAK;gEAAE;;;;;;;;;;;;;;;;;;;sDAKnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,UAAU,EAAE;oDAC5C,WAAU;;sEAEV,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDAAY;;;;;;;8DAItC,8OAAC,2HAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,SAAS;;sEACjC,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;8CAOzC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDACV,KAAK,WAAW;;;;;;;;;;;gCAKpB,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,8OAAC;oDAEC,WAAU;;sEAEV,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDACd;;mDAJI;;;;;;;;;;;;;;;;gCAYd,SAAS,MAAM,GAAG,mBACjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAA2C;gDAC7C,SAAS,MAAM,GAAG;gDAAE;;;;;;;sDAEhC,8OAAC;4CAAI,WAAU;sDACZ,SACE,MAAM,CAAC,CAAC,GAAG,QAAU,UAAU,cAC/B,KAAK,CAAC,GAAG,GAAG,6BAA6B;6CACzC,GAAG,CAAC,CAAC,6BACJ,8OAAC;oDAEC,WAAU;oDACV,SAAS;wDACP,MAAM,YAAY,SAAS,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,EAAE;wDAClE,gBAAgB;wDAChB,QAAQ;wDACR,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,aAAa,EAAE,EAAE;oDACzC;;sEAEA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,KAAK,aAAa,YAAY;oEAC9B,KAAK,aAAa,KAAK;oEACvB,WAAU;oEACV,SAAS,CAAC;wEACR,MAAM,SAAS,EAAE,MAAM;wEACvB,OAAO,KAAK,CAAC,OAAO,GAAG;oEACzB;;;;;;8EAEF,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAGpB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,aAAa,KAAK;;;;;;8EAErB,8OAAC;oEAAE,WAAU;;wEACV,aAAa,QAAQ;wEAAC;wEAAI,YAAY,aAAa,KAAK;wEAAE;;;;;;;;;;;;;;mDA5B1D,aAAa,EAAE;;;;;;;;;;;;;;;;8CAsChC,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDAAwB;oDACvB,WAAW,KAAK,SAAS;oDACtC,cAAc,SAAS,MAAM,GAAG,mBAC/B,8OAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;0DAGvC,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,8OAAC,2HAAA,CAAA,SAAM;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5C;uCAEe", "debugId": null}}, {"offset": {"line": 1102, "column": 0}, "map": {"version": 3, "file": "arrow-left.js", "sources": ["file://D%3A/Softwares/web-lab/cwa/node_modules/lucide-react/src/icons/arrow-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC/C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1148, "column": 0}, "map": {"version": 3, "file": "calendar.js", "sources": ["file://D%3A/Softwares/web-lab/cwa/node_modules/lucide-react/src/icons/calendar.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 2v4', key: '1cmpym' }],\n  ['path', { d: 'M16 2v4', key: '4m81vk' }],\n  ['rect', { width: '18', height: '18', x: '3', y: '4', rx: '2', key: '1hopcy' }],\n  ['path', { d: 'M3 10h18', key: '8toen8' }],\n];\n\n/**\n * @component @name Calendar\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAydjQiIC8+CiAgPHBhdGggZD0iTTE2IDJ2NCIgLz4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0zIDEwaDE4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/calendar\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Calendar = createLucideIcon('calendar', __iconNode);\n\nexport default Calendar;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACvC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1212, "column": 0}, "map": {"version": 3, "file": "share-2.js", "sources": ["file://D%3A/Softwares/web-lab/cwa/node_modules/lucide-react/src/icons/share-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '18', cy: '5', r: '3', key: 'gq8acd' }],\n  ['circle', { cx: '6', cy: '12', r: '3', key: 'w7nqdw' }],\n  ['circle', { cx: '18', cy: '19', r: '3', key: '1xt0gg' }],\n  ['line', { x1: '8.59', x2: '15.42', y1: '13.51', y2: '17.49', key: '47mynk' }],\n  ['line', { x1: '15.41', x2: '8.59', y1: '6.51', y2: '10.49', key: '1n3mei' }],\n];\n\n/**\n * @component @name Share2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxOCIgY3k9IjUiIHI9IjMiIC8+CiAgPGNpcmNsZSBjeD0iNiIgY3k9IjEyIiByPSIzIiAvPgogIDxjaXJjbGUgY3g9IjE4IiBjeT0iMTkiIHI9IjMiIC8+CiAgPGxpbmUgeDE9IjguNTkiIHgyPSIxNS40MiIgeTE9IjEzLjUxIiB5Mj0iMTcuNDkiIC8+CiAgPGxpbmUgeDE9IjE1LjQxIiB4Mj0iOC41OSIgeTE9IjYuNTEiIHkyPSIxMC40OSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/share-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Share2 = createLucideIcon('share-2', __iconNode);\n\nexport default Share2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACvD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACvD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC7E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC9E,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1291, "column": 0}, "map": {"version": 3, "file": "tag.js", "sources": ["file://D%3A/Softwares/web-lab/cwa/node_modules/lucide-react/src/icons/tag.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z',\n      key: 'vktsd0',\n    },\n  ],\n  ['circle', { cx: '7.5', cy: '7.5', r: '.5', fill: 'currentColor', key: 'kqv944' }],\n];\n\n/**\n * @component @name Tag\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuNTg2IDIuNTg2QTIgMiAwIDAgMCAxMS4xNzIgMkg0YTIgMiAwIDAgMC0yIDJ2Ny4xNzJhMiAyIDAgMCAwIC41ODYgMS40MTRsOC43MDQgOC43MDRhMi40MjYgMi40MjYgMCAwIDAgMy40MiAwbDYuNTgtNi41OGEyLjQyNiAyLjQyNiAwIDAgMCAwLTMuNDJ6IiAvPgogIDxjaXJjbGUgY3g9IjcuNSIgY3k9IjcuNSIgcj0iLjUiIGZpbGw9ImN1cnJlbnRDb2xvciIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/tag\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Tag = createLucideIcon('tag', __iconNode);\n\nexport default Tag;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC,CAAA;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA;KACF,CAAA;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAE;YAAA,CAAA,CAAA,EAAI,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACnF,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1340, "column": 0}, "map": {"version": 3, "file": "skip-forward.js", "sources": ["file://D%3A/Softwares/web-lab/cwa/node_modules/lucide-react/src/icons/skip-forward.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['polygon', { points: '5 4 15 12 5 20 5 4', key: '16p6eg' }],\n  ['line', { x1: '19', x2: '19', y1: '5', y2: '19', key: 'futhcm' }],\n];\n\n/**\n * @component @name SkipForward\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjUgNCAxNSAxMiA1IDIwIDUgNCIgLz4KICA8bGluZSB4MT0iMTkiIHgyPSIxOSIgeTE9IjUiIHkyPSIxOSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/skip-forward\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SkipForward = createLucideIcon('skip-forward', __iconNode);\n\nexport default SkipForward;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA;QAAA,CAAA;YAAE,QAAQ,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC3D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACnE,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1389, "column": 0}, "map": {"version": 3, "file": "skip-back.js", "sources": ["file://D%3A/Softwares/web-lab/cwa/node_modules/lucide-react/src/icons/skip-back.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['polygon', { points: '19 20 9 12 19 4 19 20', key: 'o2sva' }],\n  ['line', { x1: '5', x2: '5', y1: '19', y2: '5', key: '1ocqjk' }],\n];\n\n/**\n * @component @name SkipBack\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjE5IDIwIDkgMTIgMTkgNCAxOSAyMCIgLz4KICA8bGluZSB4MT0iNSIgeDI9IjUiIHkxPSIxOSIgeTI9IjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/skip-back\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SkipBack = createLucideIcon('skip-back', __iconNode);\n\nexport default SkipBack;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA;QAAA,CAAA;YAAE,QAAQ,CAAyB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAS;KAAA,CAAA;IAC7D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACjE,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1438, "column": 0}, "map": {"version": 3, "file": "play.js", "sources": ["file://D%3A/Softwares/web-lab/cwa/node_modules/lucide-react/src/icons/play.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['polygon', { points: '6 3 20 12 6 21 6 3', key: '1oa8hb' }]];\n\n/**\n * @component @name Play\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjYgMyAyMCAxMiA2IDIxIDYgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Play = createLucideIcon('play', __iconNode);\n\nexport default Play;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW;QAAA,CAAE;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA,CAAA;AAa3F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1477, "column": 0}, "map": {"version": 3, "file": "pause.js", "sources": ["file://D%3A/Softwares/web-lab/cwa/node_modules/lucide-react/src/icons/pause.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { x: '14', y: '4', width: '4', height: '16', rx: '1', key: 'zuxfzm' }],\n  ['rect', { x: '6', y: '4', width: '4', height: '16', rx: '1', key: '1okwgv' }],\n];\n\n/**\n * @component @name Pause\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB4PSIxNCIgeT0iNCIgd2lkdGg9IjQiIGhlaWdodD0iMTYiIHJ4PSIxIiAvPgogIDxyZWN0IHg9IjYiIHk9IjQiIHdpZHRoPSI0IiBoZWlnaHQ9IjE2IiByeD0iMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/pause\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Pause = createLucideIcon('pause', __iconNode);\n\nexport default Pause;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAG;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,QAAQ,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAG;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,QAAQ,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC/E,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}