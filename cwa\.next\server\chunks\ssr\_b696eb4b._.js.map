{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/apis/videoBlogs.ts"], "sourcesContent": ["import apiClient from '@/lib/api/apiClient';\n\n// Types for video blog data\nexport interface VideoBlogData {\n  id: string;\n  title: string;\n  description: string;\n  // Legacy fields for file-based videos (optional for YouTube videos)\n  videoFileId?: string;\n  videoFilename?: string;\n  mimetype?: string;\n  fileSize?: number;\n  duration?: number;\n  thumbnailFileId?: string;\n  thumbnailFilename?: string;\n  // New YouTube-specific fields\n  youtubeUrl?: string;\n  youtubeVideoId?: string;\n  category: string;\n  tags: string[];\n  isActive: boolean;\n  views: number;\n  videoUrl: string;\n  thumbnailUrl: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface VideoBlogPagination {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  hasNextPage: boolean;\n  hasPrevPage: boolean;\n}\n\nexport interface VideoBlogResponse {\n  status: string;\n  data: {\n    videoBlogs: VideoBlogData[];\n    pagination: VideoBlogPagination;\n  };\n}\n\nexport interface SingleVideoBlogResponse {\n  status: string;\n  data: {\n    videoBlog: VideoBlogData;\n  };\n}\n\n// Note: Caching is handled by the backend and browser cache\n\n/**\n * Map backend video blog data to frontend format\n */\nconst mapBackendVideoBlogToFrontend = (backendVideoBlog: any): VideoBlogData => {\n  return {\n    id: backendVideoBlog._id || backendVideoBlog.id,\n    title: backendVideoBlog.title,\n    description: backendVideoBlog.description || backendVideoBlog.title || '', // Fallback for legacy data\n    videoFileId: backendVideoBlog.videoFileId,\n    videoFilename: backendVideoBlog.videoFilename,\n    mimetype: backendVideoBlog.mimetype,\n    fileSize: backendVideoBlog.fileSize,\n    duration: backendVideoBlog.duration,\n    thumbnailFileId: backendVideoBlog.thumbnailFileId,\n    thumbnailFilename: backendVideoBlog.thumbnailFilename,\n    youtubeUrl: backendVideoBlog.youtubeUrl,\n    youtubeVideoId: backendVideoBlog.youtubeVideoId,\n    category: backendVideoBlog.category || 'General',\n    tags: backendVideoBlog.tags || [],\n    isActive: backendVideoBlog.isActive,\n    views: backendVideoBlog.views,\n    videoUrl: backendVideoBlog.videoUrl,\n    thumbnailUrl: backendVideoBlog.thumbnailUrl || '',\n    createdAt: backendVideoBlog.createdAt,\n    updatedAt: backendVideoBlog.updatedAt,\n  };\n};\n\n/**\n * Get all video blogs with pagination and optional search\n */\nexport const getVideoBlogs = async (\n  page: number = 1,\n  limit: number = 12,\n  search?: string,\n  category?: string\n): Promise<{ videoBlogs: VideoBlogData[]; pagination: VideoBlogPagination }> => {\n  try {\n    console.log(\"Fetching video blogs from API\");\n    \n    // Build query parameters\n    const params = new URLSearchParams({\n      page: page.toString(),\n      limit: limit.toString(),\n    });\n\n    if (search) {\n      params.append('search', search);\n    }\n\n    if (category && category !== 'all') {\n      params.append('category', category);\n    }\n\n    const response = await apiClient.get(`/video-blogs?${params.toString()}`);\n\n    if (response.data && response.data.status === \"success\") {\n      const backendVideoBlogs = response.data.data.videoBlogs;\n      const mappedVideoBlogs = backendVideoBlogs.map(mapBackendVideoBlogToFrontend);\n\n      return {\n        videoBlogs: mappedVideoBlogs,\n        pagination: response.data.data.pagination\n      };\n    } else {\n      console.error(\"API returned unexpected format:\", response.data);\n      return {\n        videoBlogs: [],\n        pagination: {\n          currentPage: 1,\n          totalPages: 0,\n          totalItems: 0,\n          itemsPerPage: limit,\n          hasNextPage: false,\n          hasPrevPage: false\n        }\n      };\n    }\n  } catch (error) {\n    console.error(\"Error fetching video blogs:\", error);\n    return {\n      videoBlogs: [],\n      pagination: {\n        currentPage: 1,\n        totalPages: 0,\n        totalItems: 0,\n        itemsPerPage: limit,\n        hasNextPage: false,\n        hasPrevPage: false\n      }\n    };\n  }\n};\n\n/**\n * Get a single video blog by ID\n */\nexport const getVideoBlogById = async (\n  id: string\n): Promise<VideoBlogData | undefined> => {\n  try {\n    const response = await apiClient.get(`/video-blogs/${id}`);\n\n    if (response.data && response.data.status === \"success\") {\n      return mapBackendVideoBlogToFrontend(response.data.data.videoBlog);\n    } else {\n      console.error(\"API returned unexpected format:\", response.data);\n      return undefined;\n    }\n  } catch (error) {\n    console.error(`Error fetching video blog with ID ${id}:`, error);\n    return undefined;\n  }\n};\n\n\n\n/**\n * Create a new video blog (Admin only)\n */\nexport const createVideoBlog = async (\n  videoBlogData: {\n    title: string;\n    description: string;\n    videoFileId?: string;\n    videoFilename?: string;\n    mimetype?: string;\n    fileSize?: number;\n    duration?: number;\n    thumbnailFileId?: string;\n    thumbnailFilename?: string;\n    videoUrl: string;\n    thumbnailUrl: string;\n    youtubeUrl?: string;\n    youtubeVideoId?: string;\n    category?: string;\n    tags?: string[];\n  }\n): Promise<VideoBlogData | null> => {\n  try {\n    const response = await apiClient.post('/video-blogs', videoBlogData, {\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    if (response.data && response.data.status === \"success\") {\n      return mapBackendVideoBlogToFrontend(response.data.data.videoBlog);\n    } else {\n      console.error(\"API returned unexpected format:\", response.data);\n      return null;\n    }\n  } catch (error) {\n    console.error(\"Error creating video blog:\", error);\n    throw error;\n  }\n};\n\n/**\n * Update a video blog (Admin only)\n */\nexport const updateVideoBlog = async (\n  id: string,\n  updates: {\n    title?: string;\n    description?: string;\n    isActive?: boolean;\n    videoUrl?: string;\n    thumbnailUrl?: string;\n    duration?: number;\n    videoFileId?: string;\n    videoFilename?: string;\n    mimetype?: string;\n    fileSize?: number;\n    thumbnailFileId?: string;\n    thumbnailFilename?: string;\n    youtubeUrl?: string;\n    youtubeVideoId?: string;\n    category?: string;\n    tags?: string[];\n  }\n): Promise<VideoBlogData | null> => {\n  try {\n    const response = await apiClient.patch(`/video-blogs/${id}`, updates);\n\n    if (response.data && response.data.status === \"success\") {\n      return mapBackendVideoBlogToFrontend(response.data.data.videoBlog);\n    } else {\n      console.error(\"API returned unexpected format:\", response.data);\n      return null;\n    }\n  } catch (error) {\n    console.error(`Error updating video blog with ID ${id}:`, error);\n    throw error;\n  }\n};\n\n/**\n * Delete a video blog (Admin only)\n */\nexport const deleteVideoBlog = async (id: string): Promise<boolean> => {\n  try {\n    const response = await apiClient.delete(`/video-blogs/${id}`);\n\n    if (response.data && response.data.status === \"success\") {\n      return true;\n    } else {\n      console.error(\"API returned unexpected format:\", response.data);\n      return false;\n    }\n  } catch (error) {\n    console.error(`Error deleting video blog with ID ${id}:`, error);\n    throw error;\n  }\n};\n\n/**\n * Format file size for display\n */\nexport const formatFileSize = (bytes: number): string => {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\n\n/**\n * Format duration for display\n */\nexport const formatDuration = (seconds: number): string => {\n  if (!seconds) return '0:00';\n\n  const hours = Math.floor(seconds / 3600);\n  const minutes = Math.floor((seconds % 3600) / 60);\n  const remainingSeconds = Math.floor(seconds % 60);\n\n  if (hours > 0) {\n    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n  } else {\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n};\n\n// YouTube-specific utility functions\n\n/**\n * Extract YouTube video ID from URL\n */\nexport const extractYouTubeVideoId = (url: string): string | null => {\n  const regex = /(?:youtube\\.com\\/(?:[^\\/]+\\/.+\\/|(?:v|e(?:mbed)?)\\/|.*[?&]v=)|youtu\\.be\\/)([^\"&?\\/\\s]{11})/;\n  const match = url.match(regex);\n  return match ? match[1] : null;\n};\n\n/**\n * Get YouTube thumbnail URL\n */\nexport const getYouTubeThumbnail = (videoId: string, quality: 'default' | 'medium' | 'high' | 'maxres' = 'maxres'): string => {\n  return `https://img.youtube.com/vi/${videoId}/${quality}default.jpg`;\n};\n\n/**\n * Get YouTube embed URL\n */\nexport const getYouTubeEmbedUrl = (videoId: string): string => {\n  return `https://www.youtube.com/embed/${videoId}`;\n};\n\n/**\n * Get unique categories from video blogs\n */\nexport const getVideoBlogCategories = async (): Promise<string[]> => {\n  try {\n    // For now, return default categories. In the future, this could fetch from the backend\n    return ['General', 'Craftsmanship', 'Tutorial', 'Behind the Scenes', 'Care Tips'];\n  } catch (error) {\n    console.error(\"Error fetching video blog categories:\", error);\n    return [];\n  }\n};\n\n/**\n * Create a YouTube video blog quickly\n */\nexport const createYouTubeVideoBlog = async (\n  youtubeUrl: string,\n  title: string,\n  description: string,\n  category: string = \"General\",\n  tags: string[] = []\n): Promise<VideoBlogData | null> => {\n  try {\n    // Extract video ID from URL\n    const videoId = extractYouTubeVideoId(youtubeUrl);\n    if (!videoId) {\n      throw new Error('Invalid YouTube URL');\n    }\n\n    // Create video blog data\n    const videoBlogData = {\n      title,\n      description,\n      videoUrl: youtubeUrl,\n      thumbnailUrl: getYouTubeThumbnail(videoId),\n      youtubeUrl,\n      youtubeVideoId: videoId,\n      category,\n      tags\n    };\n\n    return await createVideoBlog(videoBlogData);\n  } catch (error) {\n    console.error(\"Error creating YouTube video blog:\", error);\n    return null;\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;AAoDA,4DAA4D;AAE5D;;CAEC,GACD,MAAM,gCAAgC,CAAC;IACrC,OAAO;QACL,IAAI,iBAAiB,GAAG,IAAI,iBAAiB,EAAE;QAC/C,OAAO,iBAAiB,KAAK;QAC7B,aAAa,iBAAiB,WAAW,IAAI,iBAAiB,KAAK,IAAI;QACvE,aAAa,iBAAiB,WAAW;QACzC,eAAe,iBAAiB,aAAa;QAC7C,UAAU,iBAAiB,QAAQ;QACnC,UAAU,iBAAiB,QAAQ;QACnC,UAAU,iBAAiB,QAAQ;QACnC,iBAAiB,iBAAiB,eAAe;QACjD,mBAAmB,iBAAiB,iBAAiB;QACrD,YAAY,iBAAiB,UAAU;QACvC,gBAAgB,iBAAiB,cAAc;QAC/C,UAAU,iBAAiB,QAAQ,IAAI;QACvC,MAAM,iBAAiB,IAAI,IAAI,EAAE;QACjC,UAAU,iBAAiB,QAAQ;QACnC,OAAO,iBAAiB,KAAK;QAC7B,UAAU,iBAAiB,QAAQ;QACnC,cAAc,iBAAiB,YAAY,IAAI;QAC/C,WAAW,iBAAiB,SAAS;QACrC,WAAW,iBAAiB,SAAS;IACvC;AACF;AAKO,MAAM,gBAAgB,OAC3B,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,QACA;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,yBAAyB;QACzB,MAAM,SAAS,IAAI,gBAAgB;YACjC,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB;QAEA,IAAI,QAAQ;YACV,OAAO,MAAM,CAAC,UAAU;QAC1B;QAEA,IAAI,YAAY,aAAa,OAAO;YAClC,OAAO,MAAM,CAAC,YAAY;QAC5B;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,OAAO,QAAQ,IAAI;QAExE,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,WAAW;YACvD,MAAM,oBAAoB,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU;YACvD,MAAM,mBAAmB,kBAAkB,GAAG,CAAC;YAE/C,OAAO;gBACL,YAAY;gBACZ,YAAY,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU;YAC3C;QACF,OAAO;YACL,QAAQ,KAAK,CAAC,mCAAmC,SAAS,IAAI;YAC9D,OAAO;gBACL,YAAY,EAAE;gBACd,YAAY;oBACV,aAAa;oBACb,YAAY;oBACZ,YAAY;oBACZ,cAAc;oBACd,aAAa;oBACb,aAAa;gBACf;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YACL,YAAY,EAAE;YACd,YAAY;gBACV,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,cAAc;gBACd,aAAa;gBACb,aAAa;YACf;QACF;IACF;AACF;AAKO,MAAM,mBAAmB,OAC9B;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,IAAI;QAEzD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,WAAW;YACvD,OAAO,8BAA8B,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS;QACnE,OAAO;YACL,QAAQ,KAAK,CAAC,mCAAmC,SAAS,IAAI;YAC9D,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC,EAAE;QAC1D,OAAO;IACT;AACF;AAOO,MAAM,kBAAkB,OAC7B;IAkBA,IAAI;QACF,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,gBAAgB,eAAe;YACnE,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,WAAW;YACvD,OAAO,8BAA8B,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS;QACnE,OAAO;YACL,QAAQ,KAAK,CAAC,mCAAmC,SAAS,IAAI;YAC9D,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAKO,MAAM,kBAAkB,OAC7B,IACA;IAmBA,IAAI;QACF,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAS,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,IAAI,EAAE;QAE7D,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,WAAW;YACvD,OAAO,8BAA8B,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS;QACnE,OAAO;YACL,QAAQ,KAAK,CAAC,mCAAmC,SAAS,IAAI;YAC9D,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC,EAAE;QAC1D,MAAM;IACR;AACF;AAKO,MAAM,kBAAkB,OAAO;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,IAAI;QAE5D,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,WAAW;YACvD,OAAO;QACT,OAAO;YACL,QAAQ,KAAK,CAAC,mCAAmC,SAAS,IAAI;YAC9D,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC,EAAE;QAC1D,MAAM;IACR;AACF;AAKO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAKO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,mBAAmB,KAAK,KAAK,CAAC,UAAU;IAE9C,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC1G,OAAO;QACL,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACrE;AACF;AAOO,MAAM,wBAAwB,CAAC;IACpC,MAAM,QAAQ;IACd,MAAM,QAAQ,IAAI,KAAK,CAAC;IACxB,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;AAC5B;AAKO,MAAM,sBAAsB,CAAC,SAAiB,UAAoD,QAAQ;IAC/G,OAAO,CAAC,2BAA2B,EAAE,QAAQ,CAAC,EAAE,QAAQ,WAAW,CAAC;AACtE;AAKO,MAAM,qBAAqB,CAAC;IACjC,OAAO,CAAC,8BAA8B,EAAE,SAAS;AACnD;AAKO,MAAM,yBAAyB;IACpC,IAAI;QACF,uFAAuF;QACvF,OAAO;YAAC;YAAW;YAAiB;YAAY;YAAqB;SAAY;IACnF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO,EAAE;IACX;AACF;AAKO,MAAM,yBAAyB,OACpC,YACA,OACA,aACA,WAAmB,SAAS,EAC5B,OAAiB,EAAE;IAEnB,IAAI;QACF,4BAA4B;QAC5B,MAAM,UAAU,sBAAsB;QACtC,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,yBAAyB;QACzB,MAAM,gBAAgB;YACpB;YACA;YACA,UAAU;YACV,cAAc,oBAAoB;YAClC;YACA,gBAAgB;YAChB;YACA;QACF;QAEA,OAAO,MAAM,gBAAgB;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/YouTubeBlogCard.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { VideoBlogData, getYouTubeEmbedUrl } from '@/apis/videoBlogs';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Play, Eye, Calendar, User, Tag, ExternalLink } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\n\ninterface YouTubeBlogCardProps {\n  blog: VideoBlogData;\n  showDescription?: boolean;\n  className?: string;\n}\n\nconst YouTubeBlogCard: React.FC<YouTubeBlogCardProps> = ({ \n  blog, \n  showDescription = true,\n  className = \"\" \n}) => {\n  const [isHovered, setIsHovered] = useState(false);\n  const [imageError, setImageError] = useState(false);\n\n  const handleImageError = () => {\n    setImageError(true);\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  const formatViews = (views: number) => {\n    if (views >= 1000000) {\n      return `${(views / 1000000).toFixed(1)}M`;\n    } else if (views >= 1000) {\n      return `${(views / 1000).toFixed(1)}K`;\n    }\n    return views.toString();\n  };\n\n  const truncateText = (text: string, maxLength: number) => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n      className={className}\n    >\n      <Card \n        className=\"group overflow-hidden transition-all duration-300 hover:shadow-lg border-0 shadow-md\"\n        onMouseEnter={() => setIsHovered(true)}\n        onMouseLeave={() => setIsHovered(false)}\n      >\n        <CardContent className=\"p-0\">\n          {/* Video Thumbnail */}\n          <div className=\"relative aspect-video bg-gray-100 overflow-hidden\">\n            <Link href={`/blogs/${blog.id}`}>\n              {!imageError ? (\n                <Image\n                  src={blog.thumbnailUrl}\n                  alt={blog.title}\n                  fill\n                  className=\"object-cover transition-transform duration-300 group-hover:scale-105\"\n                  sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                  onError={handleImageError}\n                />\n              ) : (\n                <div className=\"w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center\">\n                  <Play className=\"w-16 h-16 text-gray-500\" />\n                </div>\n              )}\n              \n              {/* Play button overlay */}\n              <div className=\"absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                <motion.div\n                  initial={{ scale: 0.8 }}\n                  animate={{ scale: isHovered ? 1 : 0.8 }}\n                  transition={{ duration: 0.2 }}\n                  className=\"bg-white/90 rounded-full p-4 shadow-lg\"\n                >\n                  <Play className=\"w-8 h-8 text-gray-800 fill-current\" />\n                </motion.div>\n              </div>\n            </Link>\n\n            {/* Category badge */}\n            <div className=\"absolute top-3 left-3 z-10\">\n              <span className=\"bg-accent text-white text-xs font-bold px-2 py-1 rounded\">\n                {blog.category}\n              </span>\n            </div>\n\n            {/* Duration badge (if available) */}\n            <div className=\"absolute bottom-3 right-3 z-10\">\n              <span className=\"bg-black/70 text-white text-xs px-2 py-1 rounded\">\n                YouTube\n              </span>\n            </div>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-4\">\n            {/* Title */}\n            <Link href={`/blogs/${blog.id}`}>\n              <h3 className=\"font-semibold text-lg text-gray-900 line-clamp-2 leading-tight mb-2 hover:text-accent transition-colors\">\n                {blog.title}\n              </h3>\n            </Link>\n            \n            {/* Description */}\n            {showDescription && (\n              <p className=\"text-gray-600 text-sm line-clamp-3 mb-3\">\n                {truncateText(blog.description, 120)}\n              </p>\n            )}\n\n            {/* Tags */}\n            {blog.tags && blog.tags.length > 0 && (\n              <div className=\"flex flex-wrap gap-1 mb-3\">\n                {blog.tags.slice(0, 3).map((tag, index) => (\n                  <span\n                    key={index}\n                    className=\"inline-flex items-center gap-1 text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded\"\n                  >\n                    <Tag className=\"w-3 h-3\" />\n                    {tag}\n                  </span>\n                ))}\n                {blog.tags.length > 3 && (\n                  <span className=\"text-xs text-gray-500\">\n                    +{blog.tags.length - 3} more\n                  </span>\n                )}\n              </div>\n            )}\n\n            {/* Meta information */}\n            <div className=\"flex items-center justify-between text-sm text-gray-500\">\n              <div className=\"flex items-center gap-4\">\n                <div className=\"flex items-center gap-1\">\n                  <Calendar className=\"w-4 h-4\" />\n                  <span>{formatDate(blog.createdAt)}</span>\n                </div>\n                <div className=\"flex items-center gap-1\">\n                  <Eye className=\"w-4 h-4\" />\n                  <span>{formatViews(blog.views)} views</span>\n                </div>\n              </div>\n              \n              {/* Author */}\n              {blog.author && (\n                <div className=\"flex items-center gap-1\">\n                  <User className=\"w-4 h-4\" />\n                  <span className=\"text-xs\">{blog.author.name}</span>\n                </div>\n              )}\n            </div>\n\n            {/* Action buttons */}\n            <div className=\"flex items-center gap-2 mt-4\">\n              <Link href={`/blogs/${blog.id}`} className=\"flex-1\">\n                <Button variant=\"default\" size=\"sm\" className=\"w-full\">\n                  <Play className=\"w-4 h-4 mr-2\" />\n                  Watch Video\n                </Button>\n              </Link>\n              \n              {blog.youtubeUrl && (\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => window.open(blog.youtubeUrl, '_blank')}\n                  className=\"px-3\"\n                >\n                  <ExternalLink className=\"w-4 h-4\" />\n                </Button>\n              )}\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </motion.div>\n  );\n};\n\nexport default YouTubeBlogCard;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAiBA,MAAM,kBAAkD,CAAC,EACvD,IAAI,EACJ,kBAAkB,IAAI,EACtB,YAAY,EAAE,EACf;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,mBAAmB;QACvB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,SAAS,SAAS;YACpB,OAAO,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3C,OAAO,IAAI,SAAS,MAAM;YACxB,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACxC;QACA,OAAO,MAAM,QAAQ;IACvB;IAEA,MAAM,eAAe,CAAC,MAAc;QAClC,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;QACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;IACxC;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW;kBAEX,cAAA,8OAAC,yHAAA,CAAA,OAAI;YACH,WAAU;YACV,cAAc,IAAM,aAAa;YACjC,cAAc,IAAM,aAAa;sBAEjC,cAAA,8OAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;;oCAC5B,CAAC,2BACA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK,KAAK,YAAY;wCACtB,KAAK,KAAK,KAAK;wCACf,IAAI;wCACJ,WAAU;wCACV,OAAM;wCACN,SAAS;;;;;6DAGX,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAKpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,OAAO;4CAAI;4CACtB,SAAS;gDAAE,OAAO,YAAY,IAAI;4CAAI;4CACtC,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAU;sDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAMtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CACb,KAAK,QAAQ;;;;;;;;;;;0CAKlB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAmD;;;;;;;;;;;;;;;;;kCAOvE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;0CAC7B,cAAA,8OAAC;oCAAG,WAAU;8CACX,KAAK,KAAK;;;;;;;;;;;4BAKd,iCACC,8OAAC;gCAAE,WAAU;0CACV,aAAa,KAAK,WAAW,EAAE;;;;;;4BAKnC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,8OAAC;gCAAI,WAAU;;oCACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;4CAEC,WAAU;;8DAEV,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDACd;;2CAJI;;;;;oCAOR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC;wCAAK,WAAU;;4CAAwB;4CACpC,KAAK,IAAI,CAAC,MAAM,GAAG;4CAAE;;;;;;;;;;;;;0CAO/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;kEAAM,WAAW,KAAK,SAAS;;;;;;;;;;;;0DAElC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,8OAAC;;4DAAM,YAAY,KAAK,KAAK;4DAAE;;;;;;;;;;;;;;;;;;;oCAKlC,KAAK,MAAM,kBACV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAW,KAAK,MAAM,CAAC,IAAI;;;;;;;;;;;;;;;;;;0CAMjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;wCAAE,WAAU;kDACzC,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;;8DAC5C,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;oCAKpC,KAAK,UAAU,kBACd,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,UAAU,EAAE;wCAC5C,WAAU;kDAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1C;uCAEe", "debugId": null}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 774, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 999, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/YouTubeBlogGrid.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { getVideoBlogs, getVideoBlogCategories, VideoBlogData, VideoBlogPagination } from '@/apis/videoBlogs';\nimport YouTubeBlogCard from './YouTubeBlogCard';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Search, ChevronLeft, ChevronRight, Video, AlertCircle, Filter } from 'lucide-react';\nimport { toast } from 'sonner';\n\ninterface YouTubeBlogGridProps {\n  initialPage?: number;\n  itemsPerPage?: number;\n  showSearch?: boolean;\n  showFilters?: boolean;\n  showPagination?: boolean;\n  className?: string;\n}\n\nconst YouTubeBlogGrid: React.FC<YouTubeBlogGridProps> = ({\n  initialPage = 1,\n  itemsPerPage = 12,\n  showSearch = true,\n  showFilters = true,\n  showPagination = true,\n  className = \"\"\n}) => {\n  const [blogs, setBlogs] = useState<VideoBlogData[]>([]);\n  const [categories, setCategories] = useState<string[]>([]);\n  const [pagination, setPagination] = useState<VideoBlogPagination>({\n    currentPage: initialPage,\n    totalPages: 0,\n    totalItems: 0,\n    itemsPerPage,\n    hasNextPage: false,\n    hasPrevPage: false\n  });\n  const [isLoading, setIsLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [searchInput, setSearchInput] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\n\n  // Fetch categories\n  const fetchCategories = async () => {\n    try {\n      const fetchedCategories = await getVideoBlogCategories();\n      setCategories(fetchedCategories);\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n    }\n  };\n\n  // Fetch blogs\n  const fetchBlogs = async (page: number = 1, search?: string, category?: string) => {\n    setIsLoading(true);\n    try {\n      const result = await getVideoBlogs(page, itemsPerPage, search, category);\n      setBlogs(result.videoBlogs);\n      setPagination(result.pagination);\n    } catch (error) {\n      console.error('Error fetching YouTube blogs:', error);\n      toast.error('Failed to load video blogs');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Initial load\n  useEffect(() => {\n    fetchCategories();\n    fetchBlogs(initialPage);\n  }, [initialPage, itemsPerPage]);\n\n  // Handle search\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    const trimmedSearch = searchInput.trim();\n    setSearchTerm(trimmedSearch);\n    fetchBlogs(1, trimmedSearch || undefined, selectedCategory !== 'all' ? selectedCategory : undefined);\n  };\n\n  // Handle category change\n  const handleCategoryChange = (category: string) => {\n    setSelectedCategory(category);\n    fetchBlogs(1, searchTerm || undefined, category !== 'all' ? category : undefined);\n  };\n\n  // Handle page change\n  const handlePageChange = (newPage: number) => {\n    if (newPage >= 1 && newPage <= pagination.totalPages) {\n      fetchBlogs(newPage, searchTerm || undefined, selectedCategory !== 'all' ? selectedCategory : undefined);\n      // Scroll to top of grid\n      document.getElementById('youtube-blog-grid')?.scrollIntoView({ \n        behavior: 'smooth',\n        block: 'start'\n      });\n    }\n  };\n\n  // Clear filters\n  const clearFilters = () => {\n    setSearchInput('');\n    setSearchTerm('');\n    setSelectedCategory('all');\n    fetchBlogs(1);\n  };\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.5\n      }\n    }\n  };\n\n  return (\n    <div id=\"youtube-blog-grid\" className={`w-full ${className}`}>\n      {/* Header */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center gap-3 mb-4\">\n          <Video className=\"w-8 h-8 text-accent\" />\n          <h2 className=\"text-3xl font-bold text-gray-900\">Video Blogs</h2>\n        </div>\n        \n        <p className=\"text-gray-600 mb-6\">\n          Watch our collection of YouTube videos showcasing Chinioti wooden art, craftsmanship techniques, and behind-the-scenes content.\n        </p>\n\n        {/* Search and Filters */}\n        <div className=\"flex flex-col sm:flex-row gap-4 mb-6\">\n          {/* Search */}\n          {showSearch && (\n            <form onSubmit={handleSearch} className=\"flex gap-2 flex-1\">\n              <div className=\"relative flex-1\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n                <Input\n                  type=\"text\"\n                  placeholder=\"Search video blogs...\"\n                  value={searchInput}\n                  onChange={(e) => setSearchInput(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n              <Button type=\"submit\" disabled={isLoading}>\n                Search\n              </Button>\n            </form>\n          )}\n\n          {/* Category Filter */}\n          {showFilters && (\n            <div className=\"flex items-center gap-2\">\n              <Filter className=\"w-4 h-4 text-gray-500\" />\n              <Select value={selectedCategory} onValueChange={handleCategoryChange}>\n                <SelectTrigger className=\"w-[180px]\">\n                  <SelectValue placeholder=\"Select category\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">All Categories</SelectItem>\n                  {categories.map((category) => (\n                    <SelectItem key={category} value={category}>\n                      {category}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n          )}\n\n          {/* Clear filters */}\n          {(searchTerm || selectedCategory !== 'all') && (\n            <Button variant=\"outline\" onClick={clearFilters}>\n              Clear Filters\n            </Button>\n          )}\n        </div>\n\n        {/* Filter results info */}\n        {(searchTerm || selectedCategory !== 'all') && (\n          <div className=\"text-sm text-gray-600 mb-4\">\n            {pagination.totalItems > 0 ? (\n              <>\n                Found {pagination.totalItems} video{pagination.totalItems !== 1 ? 's' : ''}\n                {searchTerm && ` for \"${searchTerm}\"`}\n                {selectedCategory !== 'all' && ` in ${selectedCategory}`}\n              </>\n            ) : (\n              <>No videos found with current filters</>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Loading state */}\n      {isLoading && (\n        <div className=\"flex items-center justify-center py-12\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-accent\"></div>\n        </div>\n      )}\n\n      {/* Empty state */}\n      {!isLoading && blogs.length === 0 && (\n        <div className=\"text-center py-12\">\n          <AlertCircle className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            {searchTerm || selectedCategory !== 'all' ? 'No videos found' : 'No video blogs available'}\n          </h3>\n          <p className=\"text-gray-600 mb-4\">\n            {searchTerm || selectedCategory !== 'all'\n              ? 'Try adjusting your search terms or filters.'\n              : 'Check back later for new video content.'\n            }\n          </p>\n          {(searchTerm || selectedCategory !== 'all') && (\n            <Button onClick={clearFilters} variant=\"outline\">\n              View All Videos\n            </Button>\n          )}\n        </div>\n      )}\n\n      {/* Blog grid */}\n      {!isLoading && blogs.length > 0 && (\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\"\n        >\n          {blogs.map((blog) => (\n            <motion.div key={blog.id} variants={itemVariants}>\n              <YouTubeBlogCard blog={blog} />\n            </motion.div>\n          ))}\n        </motion.div>\n      )}\n\n      {/* Pagination */}\n      {showPagination && !isLoading && pagination.totalPages > 1 && (\n        <div className=\"mt-12 flex items-center justify-center gap-2\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => handlePageChange(pagination.currentPage - 1)}\n            disabled={!pagination.hasPrevPage}\n          >\n            <ChevronLeft className=\"w-4 h-4\" />\n            Previous\n          </Button>\n\n          <div className=\"flex items-center gap-1\">\n            {/* Page numbers */}\n            {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {\n              let pageNumber;\n              if (pagination.totalPages <= 5) {\n                pageNumber = i + 1;\n              } else if (pagination.currentPage <= 3) {\n                pageNumber = i + 1;\n              } else if (pagination.currentPage >= pagination.totalPages - 2) {\n                pageNumber = pagination.totalPages - 4 + i;\n              } else {\n                pageNumber = pagination.currentPage - 2 + i;\n              }\n\n              return (\n                <Button\n                  key={pageNumber}\n                  variant={pageNumber === pagination.currentPage ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  onClick={() => handlePageChange(pageNumber)}\n                  className=\"w-10\"\n                >\n                  {pageNumber}\n                </Button>\n              );\n            })}\n          </div>\n\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => handlePageChange(pagination.currentPage + 1)}\n            disabled={!pagination.hasNextPage}\n          >\n            Next\n            <ChevronRight className=\"w-4 h-4\" />\n          </Button>\n        </div>\n      )}\n\n      {/* Pagination info */}\n      {showPagination && !isLoading && blogs.length > 0 && (\n        <div className=\"mt-4 text-center text-sm text-gray-600\">\n          Showing {((pagination.currentPage - 1) * pagination.itemsPerPage) + 1} to{' '}\n          {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} of{' '}\n          {pagination.totalItems} video{pagination.totalItems !== 1 ? 's' : ''}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default YouTubeBlogGrid;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAVA;;;;;;;;;;;AAqBA,MAAM,kBAAkD,CAAC,EACvD,cAAc,CAAC,EACf,eAAe,EAAE,EACjB,aAAa,IAAI,EACjB,cAAc,IAAI,EAClB,iBAAiB,IAAI,EACrB,YAAY,EAAE,EACf;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;QAChE,aAAa;QACb,YAAY;QACZ,YAAY;QACZ;QACA,aAAa;QACb,aAAa;IACf;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,mBAAmB;IACnB,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,oBAAoB,MAAM,CAAA,GAAA,kHAAA,CAAA,yBAAsB,AAAD;YACrD,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,cAAc;IACd,MAAM,aAAa,OAAO,OAAe,CAAC,EAAE,QAAiB;QAC3D,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,kHAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,cAAc,QAAQ;YAC/D,SAAS,OAAO,UAAU;YAC1B,cAAc,OAAO,UAAU;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,eAAe;IACf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA,WAAW;IACb,GAAG;QAAC;QAAa;KAAa;IAE9B,gBAAgB;IAChB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,MAAM,gBAAgB,YAAY,IAAI;QACtC,cAAc;QACd,WAAW,GAAG,iBAAiB,WAAW,qBAAqB,QAAQ,mBAAmB;IAC5F;IAEA,yBAAyB;IACzB,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;QACpB,WAAW,GAAG,cAAc,WAAW,aAAa,QAAQ,WAAW;IACzE;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,IAAI,WAAW,KAAK,WAAW,WAAW,UAAU,EAAE;YACpD,WAAW,SAAS,cAAc,WAAW,qBAAqB,QAAQ,mBAAmB;YAC7F,wBAAwB;YACxB,SAAS,cAAc,CAAC,sBAAsB,eAAe;gBAC3D,UAAU;gBACV,OAAO;YACT;QACF;IACF;IAEA,gBAAgB;IAChB,MAAM,eAAe;QACnB,eAAe;QACf,cAAc;QACd,oBAAoB;QACpB,WAAW;IACb;IAEA,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,IAAG;QAAoB,WAAW,CAAC,OAAO,EAAE,WAAW;;0BAE1D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;;;;;;;kCAGnD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAKlC,8OAAC;wBAAI,WAAU;;4BAEZ,4BACC,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC,0HAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAGd,8OAAC,2HAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,UAAU;kDAAW;;;;;;;;;;;;4BAO9C,6BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAkB,eAAe;;0DAC9C,8OAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,8OAAC,2HAAA,CAAA,gBAAa;;kEACZ,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;oDACvB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,2HAAA,CAAA,aAAU;4DAAgB,OAAO;sEAC/B;2DADc;;;;;;;;;;;;;;;;;;;;;;;4BAU1B,CAAC,cAAc,qBAAqB,KAAK,mBACxC,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;0CAAc;;;;;;;;;;;;oBAOpD,CAAC,cAAc,qBAAqB,KAAK,mBACxC,8OAAC;wBAAI,WAAU;kCACZ,WAAW,UAAU,GAAG,kBACvB;;gCAAE;gCACO,WAAW,UAAU;gCAAC;gCAAO,WAAW,UAAU,KAAK,IAAI,MAAM;gCACvE,cAAc,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gCACpC,qBAAqB,SAAS,CAAC,IAAI,EAAE,kBAAkB;;yDAG1D;sCAAE;;;;;;;;;;;;;YAOT,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;YAKlB,CAAC,aAAa,MAAM,MAAM,KAAK,mBAC9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAG,WAAU;kCACX,cAAc,qBAAqB,QAAQ,oBAAoB;;;;;;kCAElE,8OAAC;wBAAE,WAAU;kCACV,cAAc,qBAAqB,QAChC,gDACA;;;;;;oBAGL,CAAC,cAAc,qBAAqB,KAAK,mBACxC,8OAAC,2HAAA,CAAA,SAAM;wBAAC,SAAS;wBAAc,SAAQ;kCAAU;;;;;;;;;;;;YAQtD,CAAC,aAAa,MAAM,MAAM,GAAG,mBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,SAAQ;gBACR,WAAU;0BAET,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAe,UAAU;kCAClC,cAAA,8OAAC,8HAAA,CAAA,UAAe;4BAAC,MAAM;;;;;;uBADR,KAAK,EAAE;;;;;;;;;;YAQ7B,kBAAkB,CAAC,aAAa,WAAW,UAAU,GAAG,mBACvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,iBAAiB,WAAW,WAAW,GAAG;wBACzD,UAAU,CAAC,WAAW,WAAW;;0CAEjC,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAIrC,8OAAC;wBAAI,WAAU;kCAEZ,MAAM,IAAI,CAAC;4BAAE,QAAQ,KAAK,GAAG,CAAC,GAAG,WAAW,UAAU;wBAAE,GAAG,CAAC,GAAG;4BAC9D,IAAI;4BACJ,IAAI,WAAW,UAAU,IAAI,GAAG;gCAC9B,aAAa,IAAI;4BACnB,OAAO,IAAI,WAAW,WAAW,IAAI,GAAG;gCACtC,aAAa,IAAI;4BACnB,OAAO,IAAI,WAAW,WAAW,IAAI,WAAW,UAAU,GAAG,GAAG;gCAC9D,aAAa,WAAW,UAAU,GAAG,IAAI;4BAC3C,OAAO;gCACL,aAAa,WAAW,WAAW,GAAG,IAAI;4BAC5C;4BAEA,qBACE,8OAAC,2HAAA,CAAA,SAAM;gCAEL,SAAS,eAAe,WAAW,WAAW,GAAG,YAAY;gCAC7D,MAAK;gCACL,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CAET;+BANI;;;;;wBASX;;;;;;kCAGF,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,iBAAiB,WAAW,WAAW,GAAG;wBACzD,UAAU,CAAC,WAAW,WAAW;;4BAClC;0CAEC,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;YAM7B,kBAAkB,CAAC,aAAa,MAAM,MAAM,GAAG,mBAC9C,8OAAC;gBAAI,WAAU;;oBAAyC;oBAC5C,CAAC,WAAW,WAAW,GAAG,CAAC,IAAI,WAAW,YAAY,GAAI;oBAAE;oBAAI;oBACzE,KAAK,GAAG,CAAC,WAAW,WAAW,GAAG,WAAW,YAAY,EAAE,WAAW,UAAU;oBAAE;oBAAI;oBACtF,WAAW,UAAU;oBAAC;oBAAO,WAAW,UAAU,KAAK,IAAI,MAAM;;;;;;;;;;;;;AAK5E;uCAEe", "debugId": null}}, {"offset": {"line": 1514, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/apis/youtubeBlogs.ts"], "sourcesContent": ["// Types for YouTube blog data\nexport interface YouTubeBlogData {\n  id: string;\n  title: string;\n  description: string;\n  youtubeUrl: string;\n  youtubeVideoId: string;\n  thumbnailUrl: string;\n  category: string;\n  tags: string[];\n  isActive: boolean;\n  views: number;\n  createdAt: string;\n  updatedAt: string;\n  author?: {\n    id: string;\n    name: string;\n    email: string;\n  };\n}\n\nexport interface YouTubeBlogPagination {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  hasNextPage: boolean;\n  hasPrevPage: boolean;\n}\n\nexport interface YouTubeBlogResponse {\n  status: string;\n  data: {\n    blogs: YouTubeBlogData[];\n    pagination: YouTubeBlogPagination;\n  };\n}\n\n// Import existing videoblogs API functions\nimport { getVideoBlogs, getVideoBlogById, createVideoBlog, VideoBlogData } from './videoBlogs';\n\n/**\n * Extract YouTube video ID from URL\n */\nexport const extractYouTubeVideoId = (url: string): string | null => {\n  const regex = /(?:youtube\\.com\\/(?:[^\\/]+\\/.+\\/|(?:v|e(?:mbed)?)\\/|.*[?&]v=)|youtu\\.be\\/)([^\"&?\\/\\s]{11})/;\n  const match = url.match(regex);\n  return match ? match[1] : null;\n};\n\n/**\n * Get YouTube thumbnail URL\n */\nexport const getYouTubeThumbnail = (videoId: string, quality: 'default' | 'medium' | 'high' | 'maxres' = 'maxres'): string => {\n  return `https://img.youtube.com/vi/${videoId}/${quality}default.jpg`;\n};\n\n/**\n * Get YouTube embed URL\n */\nexport const getYouTubeEmbedUrl = (videoId: string): string => {\n  return `https://www.youtube.com/embed/${videoId}`;\n};\n\n/**\n * Get all YouTube blogs with pagination and optional search\n * Uses existing videoblogs API and maps the data to YouTube blog format\n */\nexport const getYouTubeBlogs = async (\n  page: number = 1,\n  limit: number = 12,\n  search?: string,\n  category?: string\n): Promise<{ blogs: YouTubeBlogData[]; pagination: YouTubeBlogPagination }> => {\n  try {\n    // Use existing videoblogs API\n    const result = await getVideoBlogs(page, limit, search);\n\n    // Map videoblog data to YouTube blog format\n    const mappedBlogs: YouTubeBlogData[] = result.videoBlogs.map((videoBlog: VideoBlogData) => ({\n      id: videoBlog.id,\n      title: videoBlog.title,\n      description: videoBlog.title, // Using title as description since videoblogs don't have description\n      youtubeUrl: videoBlog.videoUrl, // Using videoUrl as youtubeUrl\n      youtubeVideoId: extractYouTubeVideoId(videoBlog.videoUrl) || '',\n      thumbnailUrl: videoBlog.thumbnailUrl || '',\n      category: 'General', // Default category since videoblogs don't have categories\n      tags: [], // Default empty tags since videoblogs don't have tags\n      isActive: videoBlog.isActive,\n      views: videoBlog.views,\n      createdAt: videoBlog.createdAt,\n      updatedAt: videoBlog.updatedAt,\n      author: {\n        id: '1',\n        name: 'Admin',\n        email: '<EMAIL>'\n      }\n    }));\n\n    // Apply category filter if needed (though videoblogs don't have categories)\n    let filteredBlogs = mappedBlogs;\n    if (category && category !== 'all') {\n      filteredBlogs = mappedBlogs.filter(blog =>\n        blog.category.toLowerCase() === category.toLowerCase()\n      );\n    }\n\n    return {\n      blogs: filteredBlogs,\n      pagination: result.pagination\n    };\n  } catch (error) {\n    console.error(\"Error fetching YouTube blogs:\", error);\n    return {\n      blogs: [],\n      pagination: {\n        currentPage: 1,\n        totalPages: 0,\n        totalItems: 0,\n        itemsPerPage: limit,\n        hasNextPage: false,\n        hasPrevPage: false\n      }\n    };\n  }\n};\n\n/**\n * Get a single YouTube blog by ID\n * Uses existing videoblogs API and maps the data to YouTube blog format\n */\nexport const getYouTubeBlogById = async (id: string): Promise<YouTubeBlogData | undefined> => {\n  try {\n    // Use existing videoblogs API\n    const videoBlog = await getVideoBlogById(id);\n\n    if (!videoBlog) {\n      return undefined;\n    }\n\n    // Map videoblog data to YouTube blog format\n    return {\n      id: videoBlog.id,\n      title: videoBlog.title,\n      description: videoBlog.title, // Using title as description since videoblogs don't have description\n      youtubeUrl: videoBlog.videoUrl, // Using videoUrl as youtubeUrl\n      youtubeVideoId: extractYouTubeVideoId(videoBlog.videoUrl) || '',\n      thumbnailUrl: videoBlog.thumbnailUrl || '',\n      category: 'General', // Default category since videoblogs don't have categories\n      tags: [], // Default empty tags since videoblogs don't have tags\n      isActive: videoBlog.isActive,\n      views: videoBlog.views,\n      createdAt: videoBlog.createdAt,\n      updatedAt: videoBlog.updatedAt,\n      author: {\n        id: '1',\n        name: 'Admin',\n        email: '<EMAIL>'\n      }\n    };\n  } catch (error) {\n    console.error(`Error fetching YouTube blog with ID ${id}:`, error);\n    return undefined;\n  }\n};\n\n/**\n * Get unique categories from YouTube blogs\n * Since videoblogs don't have categories, return default categories\n */\nexport const getYouTubeBlogCategories = async (): Promise<string[]> => {\n  try {\n    // Since videoblogs don't have categories, return some default categories\n    return ['General', 'Craftsmanship', 'Tutorial', 'Behind the Scenes'];\n  } catch (error) {\n    console.error(\"Error fetching YouTube blog categories:\", error);\n    return [];\n  }\n};\n\n/**\n * Add a new YouTube blog (for admin use)\n * Uses existing videoblogs API to create a new video blog\n */\nexport const addYouTubeBlog = async (blogData: Omit<YouTubeBlogData, 'id' | 'createdAt' | 'updatedAt' | 'views'>): Promise<YouTubeBlogData | null> => {\n  try {\n    // Extract video ID from URL\n    const videoId = extractYouTubeVideoId(blogData.youtubeUrl);\n    if (!videoId) {\n      throw new Error('Invalid YouTube URL');\n    }\n\n    // Map YouTube blog data to videoblog format\n    const videoBlogData = {\n      title: blogData.title,\n      videoFileId: videoId, // Using YouTube video ID as file ID\n      videoFilename: `${videoId}.mp4`, // Generate filename from video ID\n      mimetype: 'video/mp4',\n      fileSize: 0, // Unknown for YouTube videos\n      videoUrl: blogData.youtubeUrl,\n      thumbnailUrl: getYouTubeThumbnail(videoId),\n      isActive: blogData.isActive\n    };\n\n    // Use existing videoblogs API to create the video blog\n    const createdVideoBlog = await createVideoBlog(videoBlogData);\n\n    if (!createdVideoBlog) {\n      throw new Error('Failed to create video blog');\n    }\n\n    // Map the created videoblog back to YouTube blog format\n    return {\n      id: createdVideoBlog.id,\n      title: createdVideoBlog.title,\n      description: blogData.description,\n      youtubeUrl: createdVideoBlog.videoUrl,\n      youtubeVideoId: videoId,\n      thumbnailUrl: createdVideoBlog.thumbnailUrl || getYouTubeThumbnail(videoId),\n      category: blogData.category,\n      tags: blogData.tags,\n      isActive: createdVideoBlog.isActive,\n      views: createdVideoBlog.views,\n      createdAt: createdVideoBlog.createdAt,\n      updatedAt: createdVideoBlog.updatedAt,\n      author: blogData.author\n    };\n  } catch (error) {\n    console.error(\"Error adding YouTube blog:\", error);\n    return null;\n  }\n};\n\n/**\n * Helper function to quickly add a YouTube video blog\n * Usage: addQuickYouTubeBlog(\"https://www.youtube.com/watch?v=VIDEO_ID\", \"Video Title\", \"Video Description\", \"Category\")\n */\nexport const addQuickYouTubeBlog = async (\n  youtubeUrl: string,\n  title: string,\n  description: string,\n  category: string = \"General\",\n  tags: string[] = [],\n  author?: { id: string; name: string; email: string }\n): Promise<YouTubeBlogData | null> => {\n  const blogData = {\n    title,\n    description,\n    youtubeUrl,\n    youtubeVideoId: \"\", // Will be extracted automatically\n    thumbnailUrl: \"\", // Will be generated automatically\n    category,\n    tags,\n    isActive: true,\n    author: author || {\n      id: \"1\",\n      name: \"Admin\",\n      email: \"<EMAIL>\"\n    }\n  };\n\n  return await addYouTubeBlog(blogData);\n};\n\n// Example usage (you can call this function to add videos):\n// addQuickYouTubeBlog(\n//   \"https://www.youtube.com/watch?v=dQw4w9WgXcQ\",\n//   \"How to Care for Wooden Furniture\",\n//   \"Learn the best practices for maintaining your wooden furniture to ensure it lasts for generations.\",\n//   \"Care Tips\",\n//   [\"furniture\", \"care\", \"maintenance\", \"wood\"]\n// );\n"], "names": [], "mappings": "AAAA,8BAA8B;;;;;;;;;;;AAsC9B,2CAA2C;AAC3C;;AAKO,MAAM,wBAAwB,CAAC;IACpC,MAAM,QAAQ;IACd,MAAM,QAAQ,IAAI,KAAK,CAAC;IACxB,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;AAC5B;AAKO,MAAM,sBAAsB,CAAC,SAAiB,UAAoD,QAAQ;IAC/G,OAAO,CAAC,2BAA2B,EAAE,QAAQ,CAAC,EAAE,QAAQ,WAAW,CAAC;AACtE;AAKO,MAAM,qBAAqB,CAAC;IACjC,OAAO,CAAC,8BAA8B,EAAE,SAAS;AACnD;AAMO,MAAM,kBAAkB,OAC7B,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,QACA;IAEA,IAAI;QACF,8BAA8B;QAC9B,MAAM,SAAS,MAAM,CAAA,GAAA,kHAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,OAAO;QAEhD,4CAA4C;QAC5C,MAAM,cAAiC,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,YAA6B,CAAC;gBAC1F,IAAI,UAAU,EAAE;gBAChB,OAAO,UAAU,KAAK;gBACtB,aAAa,UAAU,KAAK;gBAC5B,YAAY,UAAU,QAAQ;gBAC9B,gBAAgB,sBAAsB,UAAU,QAAQ,KAAK;gBAC7D,cAAc,UAAU,YAAY,IAAI;gBACxC,UAAU;gBACV,MAAM,EAAE;gBACR,UAAU,UAAU,QAAQ;gBAC5B,OAAO,UAAU,KAAK;gBACtB,WAAW,UAAU,SAAS;gBAC9B,WAAW,UAAU,SAAS;gBAC9B,QAAQ;oBACN,IAAI;oBACJ,MAAM;oBACN,OAAO;gBACT;YACF,CAAC;QAED,4EAA4E;QAC5E,IAAI,gBAAgB;QACpB,IAAI,YAAY,aAAa,OAAO;YAClC,gBAAgB,YAAY,MAAM,CAAC,CAAA,OACjC,KAAK,QAAQ,CAAC,WAAW,OAAO,SAAS,WAAW;QAExD;QAEA,OAAO;YACL,OAAO;YACP,YAAY,OAAO,UAAU;QAC/B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,OAAO,EAAE;YACT,YAAY;gBACV,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,cAAc;gBACd,aAAa;gBACb,aAAa;YACf;QACF;IACF;AACF;AAMO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,8BAA8B;QAC9B,MAAM,YAAY,MAAM,CAAA,GAAA,kHAAA,CAAA,mBAAgB,AAAD,EAAE;QAEzC,IAAI,CAAC,WAAW;YACd,OAAO;QACT;QAEA,4CAA4C;QAC5C,OAAO;YACL,IAAI,UAAU,EAAE;YAChB,OAAO,UAAU,KAAK;YACtB,aAAa,UAAU,KAAK;YAC5B,YAAY,UAAU,QAAQ;YAC9B,gBAAgB,sBAAsB,UAAU,QAAQ,KAAK;YAC7D,cAAc,UAAU,YAAY,IAAI;YACxC,UAAU;YACV,MAAM,EAAE;YACR,UAAU,UAAU,QAAQ;YAC5B,OAAO,UAAU,KAAK;YACtB,WAAW,UAAU,SAAS;YAC9B,WAAW,UAAU,SAAS;YAC9B,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,OAAO;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,GAAG,CAAC,CAAC,EAAE;QAC5D,OAAO;IACT;AACF;AAMO,MAAM,2BAA2B;IACtC,IAAI;QACF,yEAAyE;QACzE,OAAO;YAAC;YAAW;YAAiB;YAAY;SAAoB;IACtE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO,EAAE;IACX;AACF;AAMO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,4BAA4B;QAC5B,MAAM,UAAU,sBAAsB,SAAS,UAAU;QACzD,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,4CAA4C;QAC5C,MAAM,gBAAgB;YACpB,OAAO,SAAS,KAAK;YACrB,aAAa;YACb,eAAe,GAAG,QAAQ,IAAI,CAAC;YAC/B,UAAU;YACV,UAAU;YACV,UAAU,SAAS,UAAU;YAC7B,cAAc,oBAAoB;YAClC,UAAU,SAAS,QAAQ;QAC7B;QAEA,uDAAuD;QACvD,MAAM,mBAAmB,MAAM,CAAA,GAAA,kHAAA,CAAA,kBAAe,AAAD,EAAE;QAE/C,IAAI,CAAC,kBAAkB;YACrB,MAAM,IAAI,MAAM;QAClB;QAEA,wDAAwD;QACxD,OAAO;YACL,IAAI,iBAAiB,EAAE;YACvB,OAAO,iBAAiB,KAAK;YAC7B,aAAa,SAAS,WAAW;YACjC,YAAY,iBAAiB,QAAQ;YACrC,gBAAgB;YAChB,cAAc,iBAAiB,YAAY,IAAI,oBAAoB;YACnE,UAAU,SAAS,QAAQ;YAC3B,MAAM,SAAS,IAAI;YACnB,UAAU,iBAAiB,QAAQ;YACnC,OAAO,iBAAiB,KAAK;YAC7B,WAAW,iBAAiB,SAAS;YACrC,WAAW,iBAAiB,SAAS;YACrC,QAAQ,SAAS,MAAM;QACzB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAMO,MAAM,sBAAsB,OACjC,YACA,OACA,aACA,WAAmB,SAAS,EAC5B,OAAiB,EAAE,EACnB;IAEA,MAAM,WAAW;QACf;QACA;QACA;QACA,gBAAgB;QAChB,cAAc;QACd;QACA;QACA,UAAU;QACV,QAAQ,UAAU;YAChB,IAAI;YACJ,MAAM;YACN,OAAO;QACT;IACF;IAEA,OAAO,MAAM,eAAe;AAC9B,GAEA,4DAA4D;CAC5D,uBAAuB;CACvB,mDAAmD;CACnD,wCAAwC;CACxC,0GAA0G;CAC1G,iBAAiB;CACjB,iDAAiD;CACjD,KAAK", "debugId": null}}, {"offset": {"line": 1708, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/AddYouTubeVideoHelper.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { addQuickYouTubeBlog, extractYouTubeVideoId } from '@/apis/youtubeBlogs';\nimport { toast } from 'sonner';\nimport { Plus, Youtube } from 'lucide-react';\n\nconst AddYouTubeVideoHelper = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [formData, setFormData] = useState({\n    youtubeUrl: '',\n    title: '',\n    description: '',\n    category: 'General',\n    tags: ''\n  });\n  const [isLoading, setIsLoading] = useState(false);\n\n  const categories = [\n    'General',\n    'Craftsmanship',\n    'Process',\n    'Education',\n    'Techniques',\n    'Care',\n    'Design',\n    'History',\n    'Sustainability'\n  ];\n\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!formData.youtubeUrl || !formData.title || !formData.description) {\n      toast.error('Please fill in all required fields');\n      return;\n    }\n\n    // Validate YouTube URL\n    const videoId = extractYouTubeVideoId(formData.youtubeUrl);\n    if (!videoId) {\n      toast.error('Please enter a valid YouTube URL');\n      return;\n    }\n\n    setIsLoading(true);\n\n    try {\n      const tags = formData.tags\n        .split(',')\n        .map(tag => tag.trim())\n        .filter(tag => tag.length > 0);\n\n      const result = await addQuickYouTubeBlog(\n        formData.youtubeUrl,\n        formData.title,\n        formData.description,\n        formData.category,\n        tags\n      );\n\n      if (result) {\n        toast.success('YouTube video added successfully!');\n        setFormData({\n          youtubeUrl: '',\n          title: '',\n          description: '',\n          category: 'General',\n          tags: ''\n        });\n        setIsOpen(false);\n        \n        // Refresh the page to show the new video\n        // window.location.reload();\n      } else {\n        toast.error('Failed to add video');\n      }\n    } catch (error) {\n      console.error('Error adding video:', error);\n      toast.error('Failed to add video');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (!isOpen) {\n    return (\n      <Button\n        // onClick={() => setIsOpen(true)}\n        // className=\"fixed bottom-4 right-4 z-50 shadow-lg\"\n        // size=\"lg\"\n      >\n        {/* <Plus className=\"w-5 h-5 mr-2\" />\n        Add YouTube Video */}\n      </Button>\n    );\n  }\n\n  // return (\n  //   <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n  //     <Card className=\"w-full max-w-2xl max-h-[90vh] overflow-y-auto\">\n  //       <CardHeader>\n  //         <CardTitle className=\"flex items-center gap-2\">\n  //           <Youtube className=\"w-5 h-5 text-red-500\" />\n  //           Add YouTube Video to Blogs\n  //         </CardTitle>\n  //       </CardHeader>\n  //       <CardContent>\n  //         <form onSubmit={handleSubmit} className=\"space-y-4\">\n  //           {/* YouTube URL */}\n  //           <div>\n  //             <label className=\"block text-sm font-medium mb-2\">\n  //               YouTube URL *\n  //             </label>\n  //             <Input\n  //               type=\"url\"\n  //               placeholder=\"https://www.youtube.com/watch?v=...\"\n  //               value={formData.youtubeUrl}\n  //               onChange={(e) => handleInputChange('youtubeUrl', e.target.value)}\n  //               required\n  //             />\n  //           </div>\n\n  //           {/* Title */}\n  //           <div>\n  //             <label className=\"block text-sm font-medium mb-2\">\n  //               Video Title *\n  //             </label>\n  //             <Input\n  //               type=\"text\"\n  //               placeholder=\"Enter video title\"\n  //               value={formData.title}\n  //               onChange={(e) => handleInputChange('title', e.target.value)}\n  //               required\n  //             />\n  //           </div>\n\n  //           {/* Description */}\n  //           <div>\n  //             <label className=\"block text-sm font-medium mb-2\">\n  //               Description *\n  //             </label>\n  //             <textarea\n  //               className=\"w-full p-3 border border-gray-300 rounded-md resize-none\"\n  //               rows={4}\n  //               placeholder=\"Enter video description\"\n  //               value={formData.description}\n  //               onChange={(e) => handleInputChange('description', e.target.value)}\n  //               required\n  //             />\n  //           </div>\n\n  //           {/* Category */}\n  //           <div>\n  //             <label className=\"block text-sm font-medium mb-2\">\n  //               Category\n  //             </label>\n  //             <Select\n  //               value={formData.category}\n  //               onValueChange={(value) => handleInputChange('category', value)}\n  //             >\n  //               <SelectTrigger>\n  //                 <SelectValue />\n  //               </SelectTrigger>\n  //               <SelectContent>\n  //                 {categories.map((category) => (\n  //                   <SelectItem key={category} value={category}>\n  //                     {category}\n  //                   </SelectItem>\n  //                 ))}\n  //               </SelectContent>\n  //             </Select>\n  //           </div>\n\n  //           {/* Tags */}\n  //           <div>\n  //             <label className=\"block text-sm font-medium mb-2\">\n  //               Tags (comma-separated)\n  //             </label>\n  //             <Input\n  //               type=\"text\"\n  //               placeholder=\"woodworking, traditional, carving\"\n  //               value={formData.tags}\n  //               onChange={(e) => handleInputChange('tags', e.target.value)}\n  //             />\n  //           </div>\n\n  //           {/* Buttons */}\n  //           <div className=\"flex gap-3 pt-4\">\n  //             <Button\n  //               type=\"button\"\n  //               variant=\"outline\"\n  //               onClick={() => setIsOpen(false)}\n  //               disabled={isLoading}\n  //               className=\"flex-1\"\n  //             >\n  //               Cancel\n  //             </Button>\n  //             <Button\n  //               type=\"submit\"\n  //               disabled={isLoading}\n  //               className=\"flex-1\"\n  //             >\n  //               {isLoading ? 'Adding...' : 'Add Video'}\n  //             </Button>\n  //           </div>\n  //         </form>\n  //       </CardContent>\n  //     </Card>\n  //   </div>\n  // );\n};\n\nexport default AddYouTubeVideoHelper;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAIA;AACA;AARA;;;;;;AAWA,MAAM,wBAAwB;IAC5B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,YAAY;QACZ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;IACR;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,UAAU,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,WAAW,EAAE;YACpE,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,uBAAuB;QACvB,MAAM,UAAU,CAAA,GAAA,oHAAA,CAAA,wBAAqB,AAAD,EAAE,SAAS,UAAU;QACzD,IAAI,CAAC,SAAS;YACZ,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QAEb,IAAI;YACF,MAAM,OAAO,SAAS,IAAI,CACvB,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IACnB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,GAAG;YAE9B,MAAM,SAAS,MAAM,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EACrC,SAAS,UAAU,EACnB,SAAS,KAAK,EACd,SAAS,WAAW,EACpB,SAAS,QAAQ,EACjB;YAGF,IAAI,QAAQ;gBACV,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,YAAY;oBACV,YAAY;oBACZ,OAAO;oBACP,aAAa;oBACb,UAAU;oBACV,MAAM;gBACR;gBACA,UAAU;YAEV,yCAAyC;YACzC,4BAA4B;YAC9B,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC,2HAAA,CAAA,SAAM;;;;;IASX;AAEA,WAAW;AACX,0FAA0F;AAC1F,uEAAuE;AACvE,qBAAqB;AACrB,0DAA0D;AAC1D,yDAAyD;AACzD,uCAAuC;AACvC,uBAAuB;AACvB,sBAAsB;AACtB,sBAAsB;AACtB,+DAA+D;AAC/D,gCAAgC;AAChC,kBAAkB;AAClB,iEAAiE;AACjE,8BAA8B;AAC9B,uBAAuB;AACvB,qBAAqB;AACrB,2BAA2B;AAC3B,kEAAkE;AAClE,4CAA4C;AAC5C,kFAAkF;AAClF,yBAAyB;AACzB,iBAAiB;AACjB,mBAAmB;AAEnB,0BAA0B;AAC1B,kBAAkB;AAClB,iEAAiE;AACjE,8BAA8B;AAC9B,uBAAuB;AACvB,qBAAqB;AACrB,4BAA4B;AAC5B,gDAAgD;AAChD,uCAAuC;AACvC,6EAA6E;AAC7E,yBAAyB;AACzB,iBAAiB;AACjB,mBAAmB;AAEnB,gCAAgC;AAChC,kBAAkB;AAClB,iEAAiE;AACjE,8BAA8B;AAC9B,uBAAuB;AACvB,wBAAwB;AACxB,qFAAqF;AACrF,yBAAyB;AACzB,sDAAsD;AACtD,6CAA6C;AAC7C,mFAAmF;AACnF,yBAAyB;AACzB,iBAAiB;AACjB,mBAAmB;AAEnB,6BAA6B;AAC7B,kBAAkB;AAClB,iEAAiE;AACjE,yBAAyB;AACzB,uBAAuB;AACvB,sBAAsB;AACtB,0CAA0C;AAC1C,gFAAgF;AAChF,gBAAgB;AAChB,gCAAgC;AAChC,kCAAkC;AAClC,iCAAiC;AACjC,gCAAgC;AAChC,kDAAkD;AAClD,iEAAiE;AACjE,iCAAiC;AACjC,kCAAkC;AAClC,sBAAsB;AACtB,iCAAiC;AACjC,wBAAwB;AACxB,mBAAmB;AAEnB,yBAAyB;AACzB,kBAAkB;AAClB,iEAAiE;AACjE,uCAAuC;AACvC,uBAAuB;AACvB,qBAAqB;AACrB,4BAA4B;AAC5B,gEAAgE;AAChE,sCAAsC;AACtC,4EAA4E;AAC5E,iBAAiB;AACjB,mBAAmB;AAEnB,4BAA4B;AAC5B,8CAA8C;AAC9C,sBAAsB;AACtB,8BAA8B;AAC9B,kCAAkC;AAClC,iDAAiD;AACjD,qCAAqC;AACrC,mCAAmC;AACnC,gBAAgB;AAChB,uBAAuB;AACvB,wBAAwB;AACxB,sBAAsB;AACtB,8BAA8B;AAC9B,qCAAqC;AACrC,mCAAmC;AACnC,gBAAgB;AAChB,wDAAwD;AACxD,wBAAwB;AACxB,mBAAmB;AACnB,kBAAkB;AAClB,uBAAuB;AACvB,cAAc;AACd,WAAW;AACX,KAAK;AACP;uCAEe", "debugId": null}}, {"offset": {"line": 1910, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/app/%28root%29/blogs/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport YouTubeBlogGrid from \"@/components/YouTubeBlogGrid\";\r\nimport AddYouTubeVideoHelper from \"@/components/AddYouTubeVideoHelper\";\r\n\r\nconst Blogs = () => {\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      {/* Hero Section */}\r\n      <section className=\"py-16 bg-white\">\r\n        <div className=\"container mx-auto px-4\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            className=\"text-center max-w-4xl mx-auto\"\r\n          >\r\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\r\n              Video <span className=\"text-accent\">Blogs</span>\r\n            </h1>\r\n            <p className=\"text-lg md:text-xl text-gray-600 mb-8\">\r\n              Watch our collection of YouTube videos showcasing Chinioti wooden art, traditional craftsmanship techniques, and behind-the-scenes content.\r\n            </p>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* YouTube Blog Grid */}\r\n      <section className=\"py-16\">\r\n        <div className=\"container mx-auto px-4\">\r\n          <YouTubeBlogGrid\r\n            itemsPerPage={12}\r\n            showSearch={true}\r\n            showFilters={true}\r\n            showPagination={true}\r\n            className=\"max-w-7xl mx-auto\"\r\n          />\r\n        </div>\r\n      </section>\r\n\r\n      {/* Add Video Helper */}\r\n      <AddYouTubeVideoHelper />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Blogs;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,QAAQ;IAEZ,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;;oCAAsC;kDAC5C,8OAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAEtC,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;;;;;;;;;;;0BAQ3D,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,8HAAA,CAAA,UAAe;wBACd,cAAc;wBACd,YAAY;wBACZ,aAAa;wBACb,gBAAgB;wBAChB,WAAU;;;;;;;;;;;;;;;;0BAMhB,8OAAC,oIAAA,CAAA,UAAqB;;;;;;;;;;;AAG5B;uCAEe", "debugId": null}}]}