"use client";

import React, { useState } from 'react';
import { VideoBlogData, getYouTubeEmbedUrl } from '@/apis/videoBlogs';
import { Card, CardContent } from '@/components/ui/card';
import { Play, Eye, Calendar, User, Tag, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';

interface YouTubeBlogCardProps {
  blog: VideoBlogData;
  showDescription?: boolean;
  className?: string;
}

const YouTubeBlogCard: React.FC<YouTubeBlogCardProps> = ({ 
  blog, 
  showDescription = true,
  className = "" 
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatViews = (views: number) => {
    if (views >= 1000000) {
      return `${(views / 1000000).toFixed(1)}M`;
    } else if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K`;
    }
    return views.toString();
  };

  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={className}
    >
      <Card 
        className="group overflow-hidden transition-all duration-300 hover:shadow-lg border-0 shadow-md"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <CardContent className="p-0">
          {/* Video Thumbnail */}
          <div className="relative aspect-video bg-gray-100 overflow-hidden">
            <Link href={`/blogs/${blog.id}`}>
              {!imageError ? (
                <Image
                  src={blog.thumbnailUrl}
                  alt={blog.title}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  onError={handleImageError}
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                  <Play className="w-16 h-16 text-gray-500" />
                </div>
              )}
              
              {/* Play button overlay */}
              <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <motion.div
                  initial={{ scale: 0.8 }}
                  animate={{ scale: isHovered ? 1 : 0.8 }}
                  transition={{ duration: 0.2 }}
                  className="bg-white/90 rounded-full p-4 shadow-lg"
                >
                  <Play className="w-8 h-8 text-gray-800 fill-current" />
                </motion.div>
              </div>
            </Link>

            {/* Category badge */}
            <div className="absolute top-3 left-3 z-10">
              <span className="bg-accent text-white text-xs font-bold px-2 py-1 rounded">
                {blog.category}
              </span>
            </div>

            {/* Duration badge (if available) */}
            <div className="absolute bottom-3 right-3 z-10">
              <span className="bg-black/70 text-white text-xs px-2 py-1 rounded">
                YouTube
              </span>
            </div>
          </div>

          {/* Content */}
          <div className="p-4">
            {/* Title */}
            <Link href={`/blogs/${blog.id}`}>
              <h3 className="font-semibold text-lg text-gray-900 line-clamp-2 leading-tight mb-2 hover:text-accent transition-colors">
                {blog.title}
              </h3>
            </Link>
            
            {/* Description */}
            {showDescription && (
              <p className="text-gray-600 text-sm line-clamp-3 mb-3">
                {truncateText(blog.description, 120)}
              </p>
            )}

            {/* Tags */}
            {blog.tags && blog.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-3">
                {blog.tags.slice(0, 3).map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center gap-1 text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded"
                  >
                    <Tag className="w-3 h-3" />
                    {tag}
                  </span>
                ))}
                {blog.tags.length > 3 && (
                  <span className="text-xs text-gray-500">
                    +{blog.tags.length - 3} more
                  </span>
                )}
              </div>
            )}

            {/* Meta information */}
            <div className="flex items-center justify-between text-sm text-gray-500">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(blog.createdAt)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Eye className="w-4 h-4" />
                  <span>{formatViews(blog.views)} views</span>
                </div>
              </div>
              
              {/* Author */}
              {blog.author && (
                <div className="flex items-center gap-1">
                  <User className="w-4 h-4" />
                  <span className="text-xs">{blog.author.name}</span>
                </div>
              )}
            </div>

            {/* Action buttons */}
            <div className="flex items-center gap-2 mt-4">
              <Link href={`/blogs/${blog.id}`} className="flex-1">
                <Button variant="default" size="sm" className="w-full">
                  <Play className="w-4 h-4 mr-2" />
                  Watch Video
                </Button>
              </Link>
              
              {blog.youtubeUrl && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(blog.youtubeUrl, '_blank')}
                  className="px-3"
                >
                  <ExternalLink className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default YouTubeBlogCard;
